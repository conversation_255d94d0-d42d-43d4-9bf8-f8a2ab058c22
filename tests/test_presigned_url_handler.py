import unittest
import json
from unittest.mock import MagicMock
from botocore.exceptions import ClientError


from src.lambda_handler import get_presigned_url_handler, get_presigned_url


class TestPresignedUrlHandler(unittest.TestCase):

    def test_presigned_url_handler_success(self):
        """Test successful generation of presigned URL"""

        mock_s3 = MagicMock()
        mock_s3.generate_presigned_url.return_value = 'https://test-bucket.s3.amazonaws.com/uploads/test-key'

        event = {
            'body': json.dumps({
                'fileName': 'test.csv',
                'fileType': 'text/csv'
            })
        }

        response = get_presigned_url_handler(
            event, {}, s3=mock_s3, bucket_name='test-bucket')

        self.assertEqual(response['statusCode'], 200)
        body = json.loads(response['body'])
        self.assertIn('presignedUrl', body)
        self.assertIn('key', body)
        self.assertIn('bucket', body)
        self.assertEqual(body['fileName'], 'test.csv')
        self.assertEqual(body['fileType'], 'text/csv')
        self.assertEqual(body['bucket'], 'test-bucket')

        mock_s3.generate_presigned_url.assert_called_once()
        args, kwargs = mock_s3.generate_presigned_url.call_args
        self.assertEqual(args[0], 'put_object')
        self.assertEqual(kwargs['Params']['Bucket'], 'test-bucket')
        self.assertTrue(kwargs['Params']['Key'].startswith('uploads/'))
        self.assertTrue(kwargs['Params']['Key'].endswith('.csv'))

    def test_presigned_url_handler_missing_fields(self):
        """Test error handling when required fields are missing"""

        mock_s3 = MagicMock()

        event = {
            'body': json.dumps({
                'fileType': 'text/csv'
            })
        }

        response = get_presigned_url_handler(
            event, {}, s3=mock_s3, bucket_name='test-bucket')
        self.assertEqual(response['statusCode'], 400)
        body = json.loads(response['body'])
        self.assertIn('message', body)
        self.assertTrue('required' in body['message'].lower())

        event = {
            'body': json.dumps({
                'fileName': 'test.csv'
            })
        }

        response = get_presigned_url_handler(
            event, {}, s3=mock_s3, bucket_name='test-bucket')
        self.assertEqual(response['statusCode'], 400)
        body = json.loads(response['body'])
        self.assertIn('message', body)
        self.assertTrue('required' in body['message'].lower())

    def test_presigned_url_handler_invalid_extension(self):
        """Test error handling when file extension is invalid"""

        mock_s3 = MagicMock()

        event = {
            'body': json.dumps({
                'fileName': 'test.txt',
                'fileType': 'text/plain'
            })
        }

        response = get_presigned_url_handler(
            event, {}, s3=mock_s3, bucket_name='test-bucket')
        self.assertEqual(response['statusCode'], 400)
        body = json.loads(response['body'])
        self.assertIn('message', body)
        self.assertTrue('csv' in body['message'].lower())

    def test_presigned_url_handler_s3_error(self):
        """Test error handling when S3 client throws an error"""

        mock_s3 = MagicMock()
        mock_s3.generate_presigned_url.side_effect = ClientError(
            {'Error': {'Code': '500', 'Message': 'Test error'}},
            'generate_presigned_url'
        )

        presigned_url, error = get_presigned_url(
            s3=mock_s3,
            bucket_name='test-bucket',
            file_key='uploads/test.csv',
            content_type='text/csv'
        )

        self.assertIsNone(presigned_url)
        self.assertIsNotNone(error)

        event = {
            'body': json.dumps({
                'fileName': 'test.csv',
                'fileType': 'text/csv'
            })
        }

        error_mock_s3 = MagicMock()
        error_mock_s3.generate_presigned_url.side_effect = ClientError(
            {'Error': {'Code': '500', 'Message': 'Test error'}},
            'generate_presigned_url'
        )

        response = get_presigned_url_handler(
            event, {}, s3=error_mock_s3, bucket_name='test-bucket')

        self.assertEqual(response['statusCode'], 500)
        body = json.loads(response['body'])
        self.assertIn('message', body)
        self.assertTrue('error' in body['message'].lower())


if __name__ == '__main__':
    unittest.main()
