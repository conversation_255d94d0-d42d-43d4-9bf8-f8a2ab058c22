# HotSpot Detection Backend

This is the backend component of the HotSpot Detection system, which provides APIs for processing datasets and detecting hotspots using advanced subgroup discovery algorithms.

## Features

- Upload CSV datasets to S3
- Submit analysis jobs to a Ray cluster running on EMR
- Get job status and track processing progress
- Retrieve analysis results
- Secure API access with AWS IAM authentication

## Architecture

The backend consists of the following components:
- Lambda functions for API handlers
- Amazon S3 for data storage
- API Gateway for REST API endpoints
- Amazon EMR for running the Ray cluster for parallel processing
- DynamoDB for tracking job status and metadata

## Prerequisites

- Python 3.11+
- AWS CLI configured with appropriate credentials
- An AWS account with permissions to create resources
- S3 bucket with versioning enabled for deployment artifacts

## Development Setup

1. Clone this repository
2. Create a virtual environment and install dependencies:
```bash
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```
3. Run tests:
```bash
make test
```

# Deployment Guide for EMR Ray Cluster with VPC Endpoints

This guide explains the modified architecture where the VPC infrastructure has been separated into its own CloudFormation template, and DynamoDB and S3 VPC endpoints have been added.

## Architecture Overview

The solution is now split into three primary components:

1. **VPC Infrastructure** (`vpc-template.yaml`)
   - Creates a VPC with public and private subnets
   - Sets up NAT Gateway for outbound internet access from private subnet
   - Adds DynamoDB and S3 VPC endpoints to enable direct communications without going through NAT
   - Resources with imports/exports to share across stacks

2. **Ray EMR Cluster** (`ray-emr-template-modified.yaml`)
   - Creates EMR cluster with Ray installed
   - References the VPC infrastructure using CloudFormation exports
   - Maintains security groups and bootstrap scripts

3. **Application Stack** (`template.yaml`)
   - Creates API Gateway, Lambda functions, and DynamoDB tables
   - References the Ray EMR cluster configuration

## Directory Structure

```
project/
├── cloudformation/
│   ├── vpc-template.yaml         # New VPC template
│   ├── ray-emr-template.yaml     # Modified Ray EMR template
│   └── template.yaml             # Main application template
├── src/
│   └── ...                       # Application code
├── tests/
│   └── ...                       # Test code
└── Makefile                      # Updated deployment commands
```

## Deployment Process

The deployment has been split into three stages that can be run independently or together:

### Option 1: Deploy Everything at Once

```bash
make deploy STACK_NAME=your-stack-name S3_BUCKET=your-s3-bucket ENV_NAME=dev
```

### Option 2: Deploy Each Component Separately

```bash
# 1. Deploy VPC Infrastructure
make deploy-vpc ENV_NAME=dev VPC_CIDR=10.0.0.0/16

# 2. Deploy Ray EMR Cluster
make deploy-ray ENV_NAME=dev RAY_S3_BUCKET=your-ray-bucket

# 3. Deploy Application Stack
make deploy-app STACK_NAME=your-stack-name S3_BUCKET=your-s3-bucket ENV_NAME=dev
```

## VPC Endpoints Benefits

The addition of VPC endpoints for DynamoDB and S3 brings several advantages:

1. **Reduced NAT Gateway Costs**: Traffic to S3 and DynamoDB won't go through the NAT Gateway, reducing data transfer costs.

2. **Improved Security**: Traffic stays within AWS network and doesn't traverse the public internet.

3. **Better Performance**: Direct connection to AWS services can result in lower latency.

4. **Increased Reliability**: Eliminates potential NAT Gateway bottlenecks and points of failure.

## Implementation Details

### VPC Template

The VPC template creates all the networking infrastructure and exports the important resource IDs (VPC ID, subnet IDs) for reference by other stacks.

### Ray EMR Template

The Ray EMR template has been modified to reference the VPC infrastructure using CloudFormation imports rather than creating these resources itself.

### Makefile Changes

The Makefile has been updated with new targets:
- `deploy-vpc`: Deploys just the VPC infrastructure
- `deploy-ray`: Deploys just the Ray EMR cluster
- `deploy-app`: Deploys just the application stack
- `deploy`: Deploys all components in the correct order

## Usage Notes

1. When running `deploy-ray`, it will automatically read the VPC information from the VPC stack outputs.

2. When running `deploy-app`, it will read Ray cluster information from local files created during the Ray deployment.

3. For development environments, consider setting `ENV_NAME` to distinguish between environments.

## Example Commands

```bash
# Full deployment for development environment
make deploy STACK_NAME=hotspot-app-dev S3_BUCKET=deployment-bucket ENV_NAME=dev

# Full deployment for production environment
make deploy STACK_NAME=hotspot-app-prod S3_BUCKET=deployment-bucket ENV_NAME=prod VPC_CIDR=10.1.0.0/16

# Update only the application component
make deploy-app STACK_NAME=hotspot-app-dev S3_BUCKET=deployment-bucket ENV_NAME=dev
```

## API Endpoints

The following API endpoints are available after deployment:

- `POST /upload-url` - Get a presigned URL for uploading a dataset
- `POST /analysis-jobs/submit` - Submit a new analysis job
- `GET /analysis-jobs/status` - Get the status of an analysis job
- `GET /analysis-jobs/history` - Get the history of analysis jobs

## Monitoring and Logging

The system uses CloudWatch for monitoring and logging:

- Lambda function logs are sent to CloudWatch Logs
- EMR cluster logs are stored in the Ray S3 bucket
- API Gateway access logs can be enabled for request tracking

You can set up CloudWatch Alarms to monitor for errors or performance issues.

## Security

- AWS IAM authentication is used for API access
- S3 buckets use server-side encryption
- EMR clusters can be deployed in private subnets
- VPC security groups restrict access to the EMR cluster

## License

This project is proprietary and confidential.