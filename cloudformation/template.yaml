AWSTemplateFormatVersion: '2010-09-09'
Description: 'HotSpot Detection POC - Data Processing Component'

Parameters:
  EnvironmentName:
    Type: String
    Default: dev
    Description: Environment name (dev, test, prod)
  
  UserDataBucketName:
    Type: String
    Description: S3 bucket name for storing user uploaded data and configurations
    Default: hotspot-user-data
    
  LambdaCodeS3Bucket:
    Type: String
    Description: S3 bucket containing the Lambda deployment package
    
  LambdaCodeS3Key:
    Type: String
    Description: S3 key for the Lambda deployment package
    Default: lambda_package.zip

  Version:
    Type: String
    Description: Deployment version (used to force updates)
    Default: v1

  CORSOrigin:
    Type: String
    Description: Allowed origin for CORS
    Default: "https://beta.console.harmony.a2z.com"
    
  RayClusterAddress:
    Type: String
    Description: Address of the Ray cluster master node
    Default: ""
    
  EmrClusterId:
    Type: String
    Description: ID of the EMR cluster running Ray
    Default: ""
    
  EmrSecurityGroupId:
    Type: String
    Description: Security Group ID of the EMR cluster
    Default: ""

Conditions:
  HasEmrConfig: !Not [!Or [!Equals [!Ref EmrClusterId, ""], !Equals [!Ref EmrSecurityGroupId, ""]]]

Resources:
  UserDataBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Ref UserDataBucketName
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - HEAD
            AllowedOrigins:
              - !Ref CORSOrigin
            MaxAge: 3600

  RayJobsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub hotspot-detection-${EnvironmentName}-analysis-jobs
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: jobId
          AttributeType: S
        - AttributeName: createdAt
          AttributeType: S
        - AttributeName: userId
          AttributeType: S
      KeySchema:
        - AttributeName: jobId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserTimeIndex
          KeySchema:
            - AttributeName: userId
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true

  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: LambdaVpcAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ec2:CreateNetworkInterface
                  - ec2:DescribeNetworkInterfaces
                  - ec2:DeleteNetworkInterface
                  - ec2:AssignPrivateIpAddresses
                  - ec2:UnassignPrivateIpAddresses
                Resource: '*'
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:PutItem
                  - dynamodb:GetItem
                  - dynamodb:UpdateItem
                  - dynamodb:Query
                  - dynamodb:Scan
                Resource: 
                  - !GetAtt RayJobsTable.Arn
                  - !Sub "${RayJobsTable.Arn}/index/*"
        - PolicyName: EmrAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - elasticmapreduce:*
                Resource: '*'
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:ListBucket
                Resource:
                  - !Sub arn:aws:s3:::${UserDataBucketName}
                  - !Sub arn:aws:s3:::${UserDataBucketName}/*

  GetPresignedUrlLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub hotspot-detection-${EnvironmentName}-get-presigned-url
      Handler: lambda_handler.get_presigned_url_handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Runtime: python3.11
      Timeout: 30
      MemorySize: 256
      Layers:
        - arn:aws:lambda:us-east-1:336392948345:layer:AWSSDKPandas-Python311:20
      Environment:
        Variables:
          USER_DATA_BUCKET: !Ref UserDataBucketName
          ENVIRONMENT: !Ref EnvironmentName
      Code:
        S3Bucket: !Ref LambdaCodeS3Bucket
        S3Key: !Ref LambdaCodeS3Key
        S3ObjectVersion: !Ref Version
      Description: !Sub "HotSpot Detection Presigned URL Generator - Version ${Version}"

  RayJobSubmitterLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub hotspot-detection-${EnvironmentName}-job-submitter
      Handler: job_submitter.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Runtime: python3.11
      Timeout: 60
      MemorySize: 512
      VpcConfig:
        SecurityGroupIds:
          - !If [HasEmrConfig, !Ref LambdaToEmrSecurityGroup, !Ref "AWS::NoValue"]
        SubnetIds:
          - !If [HasEmrConfig, Fn::ImportValue: !Sub "${EnvironmentName}-PrivateSubnetId", !Ref "AWS::NoValue"]
      Layers:
        - arn:aws:lambda:us-east-1:336392948345:layer:AWSSDKPandas-Python311:20
      Environment:
        Variables:
          USER_DATA_BUCKET: !Ref UserDataBucketName
          DATA_BUCKET: !Ref UserDataBucketName
          ENVIRONMENT: !Ref EnvironmentName
          RAY_CLUSTER_ADDRESS: !Ref RayClusterAddress
          EMR_CLUSTER_ID: !Ref EmrClusterId
          RAY_JOBS_TABLE: !Ref RayJobsTable
      Code:
        S3Bucket: !Ref LambdaCodeS3Bucket
        S3Key: !Ref LambdaCodeS3Key
        S3ObjectVersion: !Ref Version
      Description: !Sub "HotSpot Detection Job Submitter - Version ${Version}"

  ApiGateway:
    Type: AWS::ApiGateway::RestApi
    Properties:
      Name: !Sub hotspot-detection-${EnvironmentName}-api
      Description: API for HotSpot Detection POC
      EndpointConfiguration:
        Types:
          - REGIONAL

  PresignedUrlResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: upload-url

  PresignedUrlOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref PresignedUrlResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'GET,POST,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: !Sub "'${CORSOrigin}'"
              method.response.header.Access-Control-Max-Age: "'7200'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true
            method.response.header.Access-Control-Max-Age: true

  PresignedUrlMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref PresignedUrlResource
      HttpMethod: POST
      AuthorizationType: AWS_IAM
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetPresignedUrlLambda.Arn}/invocations

  RayJobsResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: analysis-jobs

  RayJobSubmitResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref RayJobsResource
      PathPart: submit

  RayJobSubmitOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref RayJobSubmitResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'POST,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: !Sub "'${CORSOrigin}'"
              method.response.header.Access-Control-Max-Age: "'7200'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true
            method.response.header.Access-Control-Max-Age: true

  RayJobSubmitMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref RayJobSubmitResource
      HttpMethod: POST
      AuthorizationType: AWS_IAM
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${RayJobSubmitterLambda.Arn}/invocations

  RayJobStatusResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref RayJobsResource
      PathPart: status

  RayJobStatusOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref RayJobStatusResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: !Sub "'${CORSOrigin}'"
              method.response.header.Access-Control-Max-Age: "'7200'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true
            method.response.header.Access-Control-Max-Age: true

  RayJobStatusMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref RayJobStatusResource
      HttpMethod: GET
      AuthorizationType: AWS_IAM
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${RayJobSubmitterLambda.Arn}/invocations

  RayJobHistoryResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref RayJobsResource
      PathPart: history

  RayJobHistoryOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref RayJobHistoryResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: !Sub "'${CORSOrigin}'"
              method.response.header.Access-Control-Max-Age: "'7200'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true
            method.response.header.Access-Control-Max-Age: true

  RayJobHistoryMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref RayJobHistoryResource
      HttpMethod: GET
      AuthorizationType: AWS_IAM
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${RayJobSubmitterLambda.Arn}/invocations
  RayJobResultsResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref RayJobsResource
      PathPart: results

  RayJobResultsOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref RayJobResultsResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: !Sub "'${CORSOrigin}'"
              method.response.header.Access-Control-Max-Age: "'7200'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true
            method.response.header.Access-Control-Max-Age: true

  RayJobResultsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref RayJobResultsResource
      HttpMethod: GET
      AuthorizationType: AWS_IAM
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${RayJobSubmitterLambda.Arn}/invocations

  ApiDeployment:
    Type: AWS::ApiGateway::Deployment
    DependsOn:
      - PresignedUrlMethod
      - PresignedUrlOptionsMethod
      - RayJobSubmitMethod
      - RayJobSubmitOptionsMethod
      - RayJobStatusMethod
      - RayJobStatusOptionsMethod
      - RayJobHistoryMethod
      - RayJobHistoryOptionsMethod
      - RayJobResultsMethod        
      - RayJobResultsOptionsMethod
    Properties:
      RestApiId: !Ref ApiGateway
      StageName: !Ref EnvironmentName

  GetPresignedUrlLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !GetAtt GetPresignedUrlLambda.Arn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*/upload-url

  RayJobSubmitLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !GetAtt RayJobSubmitterLambda.Arn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*/analysis-jobs/submit

  RayJobStatusLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !GetAtt RayJobSubmitterLambda.Arn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*/analysis-jobs/status

  RayJobHistoryLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !GetAtt RayJobSubmitterLambda.Arn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*/analysis-jobs/history
  RayJobResultsLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !GetAtt RayJobSubmitterLambda.Arn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*/analysis-jobs/results

  HarmonyAPIGatewayAccessRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub hotspot-detection-${EnvironmentName}-harmony-access-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Sid: HarmonyAPIAccess
          Action: sts:AssumeRole
          Effect: Allow
          Principal:
            AWS:
            # Harmony accounts
            - arn:aws:iam::************:root
            - arn:aws:iam::************:root
            - arn:aws:iam::************:root
            - arn:aws:iam::************:root
            - arn:aws:iam::************:root
            - arn:aws:iam::************:root
      Policies:
      - PolicyName: HotSpotDetectionApiAccessPolicy
        PolicyDocument:
          Version: "2012-10-17"
          Statement:
          - Action:
            - execute-api:Invoke
            - execute-api:ManageConnections
            Effect: Allow
            Resource:
              Fn::Sub: arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/${EnvironmentName}/*

  LambdaToEmrSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Condition: HasEmrConfig
    Properties:
      GroupDescription: Security group for Lambda to connect to EMR
      VpcId: 
        Fn::ImportValue: !Sub "${EnvironmentName}-VpcId"
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 6379
          ToPort: 6379
          DestinationSecurityGroupId: !Ref EmrSecurityGroupId
        - IpProtocol: tcp
          FromPort: 8265
          ToPort: 8265
          DestinationSecurityGroupId: !Ref EmrSecurityGroupId
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Name
          Value: !Sub "lambda-to-emr-sg-${EnvironmentName}"
          
  EmrIngressFromLambda:
    Type: AWS::EC2::SecurityGroupIngress
    Condition: HasEmrConfig
    Properties:
      GroupId: !Ref EmrSecurityGroupId
      IpProtocol: tcp
      FromPort: 6379
      ToPort: 6379
      SourceSecurityGroupId: !Ref LambdaToEmrSecurityGroup
      Description: "Allow Lambda to connect to Ray"

Outputs:
  UserDataBucketName:
    Description: Name of the S3 bucket for user uploaded data and configurations
    Value: !Ref UserDataBucket
    
  RayJobsTableName:
    Description: Name of the DynamoDB table for analysis jobs
    Value: !Ref RayJobsTable
    
  ApiEndpoint:
    Description: API Gateway endpoint URL
    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${EnvironmentName}
    
  RayJobsSubmitEndpoint:
    Description: Endpoint for submitting analysis jobs
    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${EnvironmentName}/analysis-jobs/submit
    
  RayJobsStatusEndpoint:
    Description: Endpoint for checking analysis job status
    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${EnvironmentName}/analysis-jobs/status

  RayJobsHistoryEndpoint:
    Description: Endpoint for retrieving analysis job history
    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${EnvironmentName}/analysis-jobs/history
  RayJobsResultsEndpoint:
    Description: Endpoint for retrieving job results
    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${EnvironmentName}/analysis-jobs/results