AWSTemplateFormatVersion: '2010-09-09'
Description: 'CloudFormation template for EMR Ray Cluster using existing VPC infrastructure'

Parameters:
  EnvironmentName:
    Description: Environment name for deployment
    Type: String
    Default: dev
    
  S3BucketName:
    Description: S3 bucket name for Ray logs and scripts
    Type: String
    Default: ""

Resources:
  RayS3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Ref S3BucketName
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
  
  EmrSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      VpcId: 
        Fn::ImportValue: !Sub "${EnvironmentName}-VpcId"
      GroupDescription: Security group for EMR cluster
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Name
          Value: !Sub "ray-emr-sg-${EnvironmentName}"
  
  EmrIngressRule:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref EmrSecurityGroup
      IpProtocol: -1
      SourceSecurityGroupId: !Ref EmrSecurityGroup
  
  ServiceAccessSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      VpcId: 
        Fn::ImportValue: !Sub "${EnvironmentName}-VpcId"
      GroupDescription: Security group for EMR service access
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Name
          Value: !Sub "ray-service-sg-${EnvironmentName}"

  ServiceAccessIngressRule:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref ServiceAccessSecurityGroup
      IpProtocol: tcp
      FromPort: 9443
      ToPort: 9443
      SourceSecurityGroupId: !Ref EmrSecurityGroup
  
  EmrServiceRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: elasticmapreduce.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonElasticMapReduceRole
  
  EmrEc2Role:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonElasticMapReduceforEC2Role
  
  EmrEc2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - !Ref EmrEc2Role
  
  BootstrapScriptUploaderFunction:
    Type: AWS::Lambda::Function
    DependsOn:
      - BootstrapUploaderRole
    Properties:
      Handler: index.handler
      Role: !GetAtt BootstrapUploaderRole.Arn
      Runtime: python3.11
      Timeout: 300
      Code:
        ZipFile: |
          import boto3
          import cfnresponse
          import time

          s3 = boto3.client('s3')

          def handler(event, context):
              responseData = {}
              
              if event['RequestType'] == 'Delete':
                  cfnresponse.send(event, context, cfnresponse.SUCCESS, responseData)
                  return

              try:
                  bucket_name = event['ResourceProperties']['BucketName']
                  script_content = event['ResourceProperties']['ScriptContent']
                  key = 'scripts/bootstrap_ray.sh'
                  
                  # Wait for the bucket to be available - retry logic
                  max_retries = 5
                  for attempt in range(max_retries):
                      try:
                          # Check if bucket exists
                          s3.head_bucket(Bucket=bucket_name)
                          break  # Bucket exists, proceed
                      except Exception as e:
                          if attempt == max_retries - 1:
                              # Last attempt failed, raise the exception
                              raise
                          # Wait before retrying
                          time.sleep(5)
                  
                  # Upload the bootstrap script to S3
                  s3.put_object(
                      Bucket=bucket_name,
                      Key=key,
                      Body=script_content,
                      ContentType='text/x-shellscript'
                  )
                  
                  script_url = f"s3://{bucket_name}/{key}"
                  responseData['ScriptUrl'] = script_url
                  
                  cfnresponse.send(event, context, cfnresponse.SUCCESS, responseData)
              except Exception as e:
                  print(f"Error: {str(e)}")
                  cfnresponse.send(event, context, cfnresponse.FAILED, {'Error': str(e)})

  BootstrapUploaderRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:PutObject
                  - s3:GetObject
                  - s3:HeadBucket
                  - s3:ListBucket
                Resource:
                  - "*"

  UploadBootstrapScript:
    Type: AWS::CloudFormation::CustomResource
    DependsOn: 
      - BootstrapScriptUploaderFunction
      - RayS3Bucket
    Properties:
      ServiceToken: !GetAtt BootstrapScriptUploaderFunction.Arn
      BucketName: !Ref S3BucketName
      ScriptContent: |
        #!/bin/sh
        # First, if we're on a worker node, just run our pip installs
        # They might get overwritten - we'll need to validate later
        if grep isMaster /mnt/var/lib/info/instance.json | grep false; then
            echo "Installing required packages on worker node..."
            sudo python3.11 -m pip install -U ray[all] || echo "Failed to install ray[all]"
            sudo python3.11 -m pip install -U subgroups || echo "Failed to install subgroups"
            sudo python3.11 -m pip install -U boto3 || echo "Failed to install boto3"
            sudo python3.11 -m pip install -U s3fs || echo "Failed to install s3fs"
            sudo python3.11 -m pip install -U graphviz || echo "Failed to install graphviz"
            # Verify graphviz installation
            python3.11 -c "import graphviz" || echo "Graphviz import failed"
            RAY_HEAD_IP=$(grep "\"masterHost\":" /emr/instance-controller/lib/info/extraInstanceData.json | cut -f2 -d: | cut -f2 -d\")
            sudo mkdir -p /tmp/ray/
            sudo chmod a+rwx -R /tmp/ray/
            # Wait for ray to be available on the leader node in the background
            cat >/tmp/start_ray.sh <<EOF
        #!/bin/sh
        echo -n "Waiting for Ray leader node..."
        while ( ! nc -z -v $RAY_HEAD_IP 6379); do echo -n "."; sleep 5; done
        echo -e "\nRay available...starting!"
        ray start --address=$RAY_HEAD_IP:6379 --object-manager-port=8076 --disable-usage-stats
        EOF
            chmod +x /tmp/start_ray.sh
            nohup /tmp/start_ray.sh &
            exit 0
        fi
        # Create a script that can execute in the background.
        # Otherwise, the bootstrap will wait too long and fail the cluster startup.
        cat >/tmp/install_ray.sh <<EOF
        #!/bin/sh
        # Wait for EMR to finish provisioning
        NODEPROVISIONSTATE="waiting"
        echo -n "Waiting for EMR to provision..."
        while [ ! "\$NODEPROVISIONSTATE" == "SUCCESSFUL" ]; do
            echo -n "."
            sleep 10
            NODEPROVISIONSTATE=\`sed -n '/localInstance [{]/,/[}]/{
            /nodeProvisionCheckinRecord [{]/,/[}]/ {
            /status: / { p }
            /[}]/a
            }
            /[}]/a
            }' /emr/instance-controller/lib/info/job-flow-state.txt | awk ' { print \$2 }'\`
        done
            
        echo "EMR provisioned! Continuing with installation..."
        echo "Installing required packages on master node..."
        sudo python3.11 -m pip install -U ray[all] || echo "Failed to install ray[all]"
        sudo python3.11 -m pip install -U subgroups || echo "Failed to install subgroups"
        sudo python3.11 -m pip install -U boto3 || echo "Failed to install boto3"
        sudo python3.11 -m pip install -U s3fs || echo "Failed to install s3fs"
        sudo python3.11 -m pip install -U graphviz || echo "Failed to install graphviz"
        # Verify graphviz installation
        python3.11 -c "import graphviz" || echo "Graphviz import failed"
        sudo mkdir -p /tmp/ray/
        sudo chmod a+rwx -R /tmp/ray/
        ray start --head --port=6379 --object-manager-port=8076 --disable-usage-stats
        EOF
        # Execute the script in the background
        chmod +x /tmp/install_ray.sh
        nohup /tmp/install_ray.sh &

  EmrCluster:
    Type: AWS::EMR::Cluster
    DependsOn:
      - UploadBootstrapScript
    Properties:
      Name: !Sub "ray-cluster-${EnvironmentName}"
      ReleaseLabel: emr-7.8.0
      Applications:
        - Name: Hadoop
        - Name: Spark
      LogUri: !Sub "s3://${S3BucketName}/logs/"
      ServiceRole: !Ref EmrServiceRole
      JobFlowRole: !Ref EmrEc2InstanceProfile
      Instances:
        KeepJobFlowAliveWhenNoSteps: true
        TerminationProtected: false
        Ec2SubnetId: 
          Fn::ImportValue: !Sub "${EnvironmentName}-PrivateSubnetId"
        EmrManagedMasterSecurityGroup: !Ref EmrSecurityGroup
        EmrManagedSlaveSecurityGroup: !Ref EmrSecurityGroup
        ServiceAccessSecurityGroup: !Ref ServiceAccessSecurityGroup
        MasterInstanceGroup:
          InstanceCount: 1
          InstanceType: m5.xlarge
          Market: ON_DEMAND
          Name: Master
        CoreInstanceGroup:
          InstanceCount: 2
          InstanceType: m5.xlarge
          Market: ON_DEMAND
          Name: Core
      VisibleToAllUsers: true
      BootstrapActions:
        - Name: InstallRay
          ScriptBootstrapAction:
            Path: !GetAtt UploadBootstrapScript.ScriptUrl
      Tags:
        - Key: Name
          Value: !Sub "ray-cluster-${EnvironmentName}"
        - Key: Environment
          Value: !Ref EnvironmentName

Outputs:
  ClusterId:
    Description: EMR Cluster ID
    Value: !Ref EmrCluster
    
  VpcId:
    Description: VPC ID
    Value: 
      Fn::ImportValue: !Sub "${EnvironmentName}-VpcId"
    
  EmrSecurityGroupId:
    Description: EMR Security Group ID
    Value: !Ref EmrSecurityGroup
    
  PublicSubnetId:
    Description: Public Subnet ID
    Value:
      Fn::ImportValue: !Sub "${EnvironmentName}-PublicSubnetId"
    
  PrivateSubnetId:
    Description: Private Subnet ID
    Value:
      Fn::ImportValue: !Sub "${EnvironmentName}-PrivateSubnetId"
    
  MasterPublicDNS:
    Description: Master Public DNS
    Value: !GetAtt EmrCluster.MasterPublicDNS
    
  S3BucketName:
    Description: S3 Bucket Name
    Value: !Ref S3BucketName