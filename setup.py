from setuptools import setup, find_packages

setup(
    name="hotspot-detection",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "pandas>=1.3.0",
        "boto3>=1.18.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=3.0.0",
            "flake8>=4.0.0",
            "mypy>=0.9.0",
            "build>=0.8.0",
            "twine>=4.0.0",
            "pre-commit>=2.17.0",
            "tox>=3.24.0",
            "hypothesis>=6.0.0",
        ],
    },
    author="<PERSON><PERSON><PERSON>",
    author_email="ka<PERSON><PERSON>@gmail.com",
    description="HotSpot Detection POC - Data Processing Component",
    keywords="data-processing, hotspot-detection",
    python_requires=">=3.11",
) 