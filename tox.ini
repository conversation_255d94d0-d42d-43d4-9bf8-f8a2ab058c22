[tox]
envlist = py311
isolated_build = True

[testenv]
deps =
    pytest
    pytest-cov
    flake8
    mypy
    hypothesis
commands =
    pytest {posargs:tests} --cov=src --cov-report=term-missing
    flake8 src tests
    mypy src

[testenv:lint]
deps =
    flake8
    mypy
commands =
    flake8 src tests
    mypy src

[testenv:build]
deps =
    build
    twine
commands =
    python3.11 -m build
    twine check dist/*

[flake8]
max-line-length = 100
exclude = .tox,*.egg,build,data
select = E,W,F 