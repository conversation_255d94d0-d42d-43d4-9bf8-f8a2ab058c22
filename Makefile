.PHONY: clean test lint format coverage deploy deploy-vpc deploy-ray deploy-app package help

all: clean lint test

help:
	@echo "Available commands:"
	@echo "  make clean      - Remove build artifacts and cache files"
	@echo "  make test       - Run tests with pytest"
	@echo "  make coverage   - Run tests with coverage report"
	@echo "  make lint       - Run linting checks"
	@echo "  make format     - Format code with black and isort"
	@echo "  make package    - Create deployment package"
	@echo "  make deploy     - Deploy all components (VPC, Ray EMR, Application)"
	@echo "  make deploy-vpc - Deploy only the VPC infrastructure"
	@echo "  make deploy-ray - Deploy only the Ray EMR cluster (requires VPC)"
	@echo "  make deploy-app - Deploy only the application stack (requires Ray EMR)"
	@echo "  make all        - Run clean, lint, and test"
	@echo ""
	@echo "Deployment parameters:"
	@echo "  STACK_NAME      - Name of the main application CloudFormation stack (required for deploy-app)"
	@echo "  S3_BUCKET       - S3 bucket for storing Lambda package (required for deploy-app)"
	@echo "  ENV_NAME        - Environment name (default: dev)"
	@echo "  USER_DATA_BUCKET - S3 bucket for user data and configurations (default: hotspot-user-data-{ENV_NAME})"
	@echo "  VPC_STACK_NAME  - Name of the VPC CloudFormation stack (default: hotspot-vpc-{ENV_NAME})"
	@echo "  RAY_STACK_NAME  - Name of the Ray EMR CloudFormation stack (default: hotspot-ray-{ENV_NAME})"
	@echo "  RAY_S3_BUCKET   - S3 bucket for Ray logs and scripts (default: hotspot-ray-{ENV_NAME})"
	@echo "  VPC_CIDR        - CIDR block for the VPC (default: 10.0.0.0/16)"

clean:
	@echo "Cleaning up..."
	@rm -rf build/ dist/ *.egg-info/ .coverage htmlcov/ .pytest_cache/ __pycache__/ .mypy_cache/ .hypothesis/
	@find . -type d -name __pycache__ -exec rm -rf {} +
	@find . -type d -name "*.egg-info" -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete
	@find . -type f -name "*.pyo" -delete
	@find . -type f -name "*.pyd" -delete
	@find . -type f -name ".coverage" -delete
	@find . -type f -name "coverage.xml" -delete
	@find . -type d -name "htmlcov" -exec rm -rf {} +
	@find . -type d -name ".pytest_cache" -exec rm -rf {} +
	@find . -type d -name ".mypy_cache" -exec rm -rf {} +
	@find . -type d -name ".hypothesis" -exec rm -rf {} +

test:
	@PYTHONPATH=${PYTHONPATH}:$(PWD)/src python3.11 -m pytest tests/ --cov=src --cov-report=term-missing

coverage:
	@pytest tests/ --cov=src --cov-report=html
	@echo "Coverage report generated in htmlcov/"
	@echo "Open htmlcov/index.html in your browser to view the report"

lint:
	@flake8 src/ tests/
	@mypy src/ tests/

format:
	@black src/ tests/
	@isort src/ tests/

package: clean
	@mkdir -p dist
	

	@mkdir -p ./package
	

	@rsync -av --exclude='scripts/' src/ ./package/
	

	@cd package && zip -r ../dist/lambda_package.zip *
	@rm -rf package/
	@echo "Deployment package created at dist/lambda_package.zip"

deploy:
	@$(eval ENV_NAME ?= dev)
	@echo "Starting full deployment for environment: $(ENV_NAME)"
	@make deploy-vpc ENV_NAME=$(ENV_NAME) VPC_CIDR=$(VPC_CIDR)
	@make deploy-ray ENV_NAME=$(ENV_NAME) RAY_S3_BUCKET=$(RAY_S3_BUCKET)
	@make deploy-app STACK_NAME=$(STACK_NAME) S3_BUCKET=$(S3_BUCKET) ENV_NAME=$(ENV_NAME)
	@echo "Full deployment completed successfully!"

deploy-vpc:
	@$(eval ENV_NAME ?= dev)
	@$(eval VPC_STACK_NAME ?= hotspot-vpc-$(ENV_NAME))
	@$(eval VPC_CIDR ?= 10.0.0.0/16)
	
	@echo "Deploying VPC CloudFormation stack: $(VPC_STACK_NAME)..."
	@aws cloudformation deploy \
		--template-file ./cloudformation/vpc-template.yaml \
		--stack-name $(VPC_STACK_NAME) \
		--capabilities CAPABILITY_IAM \
		--no-fail-on-empty-changeset
	
	@echo "VPC infrastructure deployed successfully!"

deploy-ray:
	@$(eval ENV_NAME ?= dev)
	@$(eval RAY_STACK_NAME = hotspot-ray-$(ENV_NAME))
	@$(eval RAY_S3_BUCKET = hotspot-ray-$(ENV_NAME))
	
	@echo "Deploying Ray EMR CloudFormation stack: $(RAY_STACK_NAME)..."
	@aws cloudformation deploy \
		--template-file ./cloudformation/ray-emr-template.yaml \
		--stack-name $(RAY_STACK_NAME) \
		--parameter-overrides \
			EnvironmentName=$(ENV_NAME) \
			S3BucketName=$(RAY_S3_BUCKET) \
		--capabilities CAPABILITY_IAM \
		--no-fail-on-empty-changeset
	
	@echo "Getting Ray EMR cluster outputs..."
	@RAY_CLUSTER_ADDRESS=$$(aws cloudformation describe-stacks --stack-name $(RAY_STACK_NAME) --query "Stacks[0].Outputs[?OutputKey=='MasterPublicDNS'].OutputValue" --output text); \
	EMR_CLUSTER_ID=$$(aws emr list-clusters --active --query "Clusters[?Name=='ray-cluster-$(ENV_NAME)'].Id" --output text); \
	EMR_SG_ID=$$(aws cloudformation describe-stacks --stack-name $(RAY_STACK_NAME) --query "Stacks[0].Outputs[?OutputKey=='EmrSecurityGroupId'].OutputValue" --output text); \
	echo "Ray EMR Cluster deployed!"; \
	echo "Ray Dashboard URL: http://$$RAY_CLUSTER_ADDRESS:8265"; \
	echo "$$RAY_CLUSTER_ADDRESS" > .ray_address; \
	echo "$$EMR_CLUSTER_ID" > .emr_cluster_id; \
	echo "$$EMR_SG_ID" > .emr_sg_id

deploy-app: package
	@if [ -z "$(STACK_NAME)" ]; then \
		echo "Error: STACK_NAME is not set. Use 'make deploy-app STACK_NAME=your-stack-name S3_BUCKET=your-s3-bucket'"; \
		exit 1; \
	fi
	@if [ -z "$(S3_BUCKET)" ]; then \
		echo "Error: S3_BUCKET is not set. Use 'make deploy-app STACK_NAME=your-stack-name S3_BUCKET=your-s3-bucket'"; \
		exit 1; \
	fi
	

	@echo "Checking S3 bucket versioning..."
	@VERSIONING_STATUS=$$(aws s3api get-bucket-versioning --bucket $(S3_BUCKET) --query 'Status' --output text); \
	if [ "$$VERSIONING_STATUS" != "Enabled" ]; then \
		echo "Error: S3 bucket $(S3_BUCKET) must have versioning enabled"; \
		exit 1; \
	fi
	

	@$(eval ENV_NAME ?= dev)
	@$(eval USER_DATA_BUCKET ?= hotspot-user-data-$(ENV_NAME))
	@$(eval LAMBDA_KEY = lambda_package.zip)
	

	@echo "Ensuring user data bucket $(USER_DATA_BUCKET) exists..."
	@aws s3api head-bucket --bucket $(USER_DATA_BUCKET) 2>/dev/null || aws s3 mb s3://$(USER_DATA_BUCKET)
	

	@echo "Uploading script to S3..."
	@aws s3 cp src/scripts/run_subgroup_discovery.py s3://$(USER_DATA_BUCKET)/scripts/run_subgroup_discovery.py
	

	@echo "Uploading Lambda package to S3..."
	@aws s3 cp dist/lambda_package.zip s3://$(S3_BUCKET)/$(LAMBDA_KEY)
	@VERSION=$$(aws s3api list-object-versions \
    --bucket $(S3_BUCKET) \
    --prefix $(LAMBDA_KEY) \
    --query 'sort_by(Versions, &LastModified)[-1].VersionId' \
    --output text) && \
    echo "Uploaded Lambda package with version ID: $$VERSION" && \
    echo "$$VERSION" > .version_id
	

	@VERSION=$$(cat .version_id 2>/dev/null || echo "") && \
	RAY_CLUSTER_ADDRESS=$$(cat .ray_address 2>/dev/null || echo "") && \
	EMR_CLUSTER_ID=$$(cat .emr_cluster_id 2>/dev/null || echo "") && \
	EMR_SG_ID=$$(cat .emr_sg_id 2>/dev/null || echo "") && \
	if aws cloudformation describe-stacks --stack-name $(STACK_NAME) > /dev/null 2>&1; then \
		echo "Updating existing stack: $(STACK_NAME)"; \
		aws cloudformation update-stack \
			--stack-name $(STACK_NAME) \
			--template-body file://cloudformation/template.yaml \
			--capabilities CAPABILITY_NAMED_IAM \
			--parameters \
				ParameterKey=EnvironmentName,ParameterValue=$(ENV_NAME) \
				ParameterKey=UserDataBucketName,ParameterValue=$(USER_DATA_BUCKET) \
				ParameterKey=LambdaCodeS3Bucket,ParameterValue=$(S3_BUCKET) \
				ParameterKey=LambdaCodeS3Key,ParameterValue=$(LAMBDA_KEY) \
				ParameterKey=Version,ParameterValue="$$VERSION" \
				ParameterKey=RayClusterAddress,ParameterValue="$$RAY_CLUSTER_ADDRESS" \
				ParameterKey=EmrClusterId,ParameterValue="$$EMR_CLUSTER_ID" \
				ParameterKey=EmrSecurityGroupId,ParameterValue="$$EMR_SG_ID" || \
		echo "No updates to be performed."; \
	else \
		echo "Creating new stack: $(STACK_NAME)"; \
		aws cloudformation create-stack \
			--stack-name $(STACK_NAME) \
			--template-body file://cloudformation/template.yaml \
			--capabilities CAPABILITY_NAMED_IAM \
			--parameters \
				ParameterKey=EnvironmentName,ParameterValue=$(ENV_NAME) \
				ParameterKey=UserDataBucketName,ParameterValue=$(USER_DATA_BUCKET) \
				ParameterKey=LambdaCodeS3Bucket,ParameterValue=$(S3_BUCKET) \
				ParameterKey=LambdaCodeS3Key,ParameterValue=$(LAMBDA_KEY) \
				ParameterKey=Version,ParameterValue="$$VERSION" \
				ParameterKey=RayClusterAddress,ParameterValue="$$RAY_CLUSTER_ADDRESS" \
				ParameterKey=EmrClusterId,ParameterValue="$$EMR_CLUSTER_ID" \
				ParameterKey=EmrSecurityGroupId,ParameterValue="$$EMR_SG_ID"; \
	fi
	

	@echo "Waiting for stack operation to complete..."
	@aws cloudformation wait stack-create-complete --stack-name $(STACK_NAME) || \
	 aws cloudformation wait stack-update-complete --stack-name $(STACK_NAME)
	@echo "Stack operation completed successfully!"
	

	@if [ -f .ray_address ]; then \
		RAY_CLUSTER_ADDRESS=$$(cat .ray_address); \
		if [ -n "$$RAY_CLUSTER_ADDRESS" ]; then \
			echo "Ray Dashboard URL: http://$$RAY_CLUSTER_ADDRESS:8265"; \
		fi; \
	fi