{"name": "@amzn/kaushbor-test", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "npm run dev", "build-harmony-app": "build-harmony --templateName react", "publish": "build-harmony --templateName react --brazil-build"}, "dependencies": {"@amzn/harmony-react-tutorials": "^2.0.3", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "sonner": "^2.0.1", "zod": "^3.24.2"}, "devDependencies": {"@amzn/harmony-types": "^1.0.36", "@tailwindcss/vite": "^4.0.14", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0-beta.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "lucide-react": "^0.482.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.14", "tw-animate-css": "^1.2.2", "typescript": "^5.0.2", "vite": "^5.2.0"}, "harmony": {"appName": "kaushbor-test", "jobCategories": [], "headers": [{"name": "Content-Security-Policy", "value": "connect-src udnjhf4hn7.execute-api.us-east-1.amazonaws.com *.s3.amazonaws.com"}]}, "harmony-version": "1.7.144", "homepage": "/kaushbor-test/", "displayName": "My awesome Harmony-kaushbor-test", "description": "Probably the coolest app you've used in a while", "bindleId": "amzn1.bindle.resource.3yz45sh5r6nrzlwxpw7a"}