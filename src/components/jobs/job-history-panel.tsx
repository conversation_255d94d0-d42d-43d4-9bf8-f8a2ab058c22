import { useState, useEffect } from 'react';
import { ListFilter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { JobHistoryItem, JobDetails } from '@/components/jobs/job-history-item';
import { hotspotApiEndpoint } from '@/config';
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableHeader,
  TableRow,
  TableHead,
} from '@/components/ui/table';

export function JobHistoryPanel() {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [jobs, setJobs] = useState<JobDetails[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchJobs = async () => {
      setLoading(true);
      setError(null);

      try {

        let url = `${hotspotApiEndpoint}/analysis-jobs/history`;
        if (statusFilter !== 'all') {
          url += `?status=${statusFilter.toUpperCase()}`;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch jobs: ${response.statusText}`);
        }

        const data = await response.json();


        const transformedJobs: JobDetails[] = data.jobs.map((job: any) => ({
          id: job.jobId,
          name: job.name || 'Untitled Analysis',
          status: job.status.toLowerCase(),
          progress: job.status === 'COMPLETED' ? 100 : job.status === 'RUNNING' ? 50 : 0,
          createdAt: new Date(job.createdAt),
          dimensions: job.parameters?.dimensions || [],
          targetMetric: {
            column: job.parameters?.targetAttribute || '',
            value: job.parameters?.targetValue || ''
          },
          maxFeatures: job.parameters?.maxFeatures || 10,
          fileName: job.parameters?.datasetKey || 'Unknown file'
        }));

        setJobs(transformedJobs);
      } catch (error) {
        console.error('Error fetching jobs:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch jobs');
        toast.error("Error loading job history", {
          description: error instanceof Error ? error.message : "Failed to fetch jobs"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchJobs();
  }, [statusFilter]);

  const filteredJobs = jobs;

  return (
    <div className="flex flex-col h-full">
      <div className="px-4 py-2 border-b">
        <div className="flex items-center gap-2">
          <ListFilter className="h-4 w-4 text-muted-foreground" />
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Jobs</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="running">Running</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <ScrollArea className="h-[calc(100vh-200px)]">
        {loading ? (
          <div className="flex items-center justify-center h-40">
            <p className="text-sm text-muted-foreground">Loading analysis jobs...</p>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-40 text-center px-4">
            <p className="text-sm text-muted-foreground">{error}</p>
            <Button
              variant="link"
              size="sm"
              className="mt-2"
              onClick={() => setStatusFilter('all')}
            >
              Try again
            </Button>
          </div>
        ) : filteredJobs.length > 0 ? (
          <div className="w-full px-4">
            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px] bg-muted/50">Name</TableHead>
                    <TableHead className="w-[120px] bg-muted/50">Status</TableHead>
                    <TableHead className="w-[150px] bg-muted/50">Created</TableHead>
                    <TableHead className="w-[150px] bg-muted/50">File</TableHead>
                    <TableHead className="w-[200px] bg-muted/50">Target</TableHead>
                    <TableHead className="w-[200px] bg-muted/50">Dimensions</TableHead>
                    <TableHead className="w-[120px] bg-muted/50">Max Features</TableHead>
                    <TableHead className="w-[200px] bg-muted/50">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredJobs.map(job => (
                    <JobHistoryItem key={job.id} job={job} />
                  ))}
                </TableBody>
              </Table>
            </div>
            <ScrollBar orientation="horizontal" />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-40 text-center px-4">
            <p className="text-sm text-muted-foreground">No analysis jobs found with the selected status.</p>
            <Button
              variant="link"
              size="sm"
              className="mt-2"
              onClick={() => setStatusFilter('all')}
            >
              Show all jobs
            </Button>
          </div>
        )}
      </ScrollArea>
    </div>
  );
}