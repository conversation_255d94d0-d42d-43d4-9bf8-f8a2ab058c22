"use client"

import * as React from "react"
import {
  ArrowUpCircleIcon,
  CameraIcon,
  ClipboardListIcon,
  DatabaseIcon,
  FileCodeIcon,
  FileIcon,
  FileTextIcon,
  HelpCircleIcon,
  PlusCircleIcon,
  SearchIcon,
  SettingsIcon,
  UploadIcon,
  BarChartIcon
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { DashboardView } from "@/dashboard/dashboard"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  quickCreate: {
    title: "Quick Create",
    icon: PlusCircleIcon,
    url: "#quick-create",
    primary: true,
    view: "quick-create" as DashboardView,
  },
  navMain: [
    {
      title: "Upload Dataset",
      url: "#dataset-upload",
      icon: UploadIcon,
      view: "quick-create" as DashboardView,
    },
    {
      title: "Analysis Results",
      url: "#job-history",
      icon: BarChartIcon,
      linkedPanel: "job-history",
      view: "job-history" as DashboardView,
    },
  ],
  navClouds: [
    {
      title: "Capture",
      icon: CameraIcon,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: FileTextIcon,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: FileCodeIcon,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "#",
      icon: SettingsIcon,
    },
    {
      title: "Get Help",
      url: "#",
      icon: HelpCircleIcon,
    },
    {
      title: "Search",
      url: "#",
      icon: SearchIcon,
    },
  ],
  documents: [
    {
      name: "Data Library",
      url: "#",
      icon: DatabaseIcon,
    },
    {
      name: "Reports",
      url: "#",
      icon: ClipboardListIcon,
    },
    {
      name: "Word Assistant",
      url: "#",
      icon: FileIcon,
    },
  ],
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  setActiveView?: React.Dispatch<React.SetStateAction<DashboardView>>;
}

export function AppSidebar({ setActiveView, ...props }: AppSidebarProps) {
  const [_, setActivePanel] = React.useState<string | null>(null);


  const handleNavItemClick = (linkedPanel?: string, view?: DashboardView) => {
    if (linkedPanel) {
      setActivePanel(linkedPanel);
    }


    if (view && setActiveView) {
      setActiveView(view);
    }
  };

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <ArrowUpCircleIcon className="h-5 w-5" />
                <span className="text-base font-semibold">Hotspot Inc.</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="flex flex-col gap-6">
        {/* Navigation with Quick Create */}
        <NavMain
          items={[data.quickCreate, ...data.navMain]}
          onItemClick={(linkedPanel, item) => handleNavItemClick(linkedPanel, item?.view)}
        />
      </SidebarContent>
      <SidebarFooter />
    </Sidebar>
  )
}
