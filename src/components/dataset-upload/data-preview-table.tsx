import {
  Table,
  TableBody,
  TableHeader,
  TableRow,
  TableCell,
  TableHead,
} from '@/components/ui/table';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Label } from '@/components/ui/label';

interface DataPreview {
  headers: string[];
  rows: string[][];
}

interface DataPreviewTableProps {
  dataPreview: DataPreview;
}

export function DataPreviewTable({ dataPreview }: DataPreviewTableProps) {
  return (
    <div className="space-y-2">
      <Label>Data Preview</Label>
      <ScrollArea className="h-100 rounded-md border overflow-hidden">
        <div className="min-w-full">
          <Table>
            <TableHeader>
              <TableRow>
                {dataPreview.headers.map((header, i) => (
                  <TableHead key={i} className="text-left text-xs font-medium whitespace-nowrap">
                    {header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {dataPreview.rows.map((row, i) => (
                <TableRow key={i}>
                  {row.map((cell, j) => (
                    <TableCell key={j} className="text-xs whitespace-nowrap">
                      {cell}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
} 