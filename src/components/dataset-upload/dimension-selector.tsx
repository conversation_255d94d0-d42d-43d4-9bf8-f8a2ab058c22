import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useState } from 'react';

interface DimensionSelectorProps {
  headers: string[];
  selectedDimensions: string[];
  onDimensionChange: (dimensions: string[]) => void;
}

export function DimensionSelector({
  headers,
  selectedDimensions,
  onDimensionChange
}: DimensionSelectorProps) {
  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAllChange = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      onDimensionChange([...headers]);
    } else {
      onDimensionChange([]);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-sm">Dimensions</CardTitle>
            <CardDescription>
              Select dimensions to include in the analysis
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="select-all"
              checked={selectAll}
              onCheckedChange={handleSelectAllChange}
              className="scale-75 data-[state=checked]:bg-primary"
            />
            <Label htmlFor="select-all" className="text-xs">Select All</Label>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-40">
          <div className="space-y-2">
            {headers.map((header) => (
              <div key={header} className="flex items-center space-x-2">
                <Checkbox
                  id={`dimension-${header}`}
                  checked={selectedDimensions.includes(header)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      onDimensionChange([...selectedDimensions, header]);
                    } else {
                      onDimensionChange(selectedDimensions.filter(h => h !== header));
                    }
                  }}
                  className="scale-90 data-[state=checked]:bg-primary"
                />
                <Label htmlFor={`dimension-${header}`} className="text-xs font-normal">
                  {header}
                </Label>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
} 