import { Input } from '@/components/ui/input';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { InfoIcon } from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface MaxFeaturesInputProps {
  maxFeatures: number;
  onMaxFeaturesChange: (maxFeatures: number) => void;
  className?: string;
}

export function MaxFeaturesInput({
  maxFeatures,
  onMaxFeaturesChange,
  className = ""
}: MaxFeaturesInputProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <CardTitle className="text-sm">Max Features</CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs text-xs">
                  Maximum number of features to consider for hotspot analysis
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-3">
          <Input
            id="max-features"
            type="number"
            min="1"
            max="100"
            value={maxFeatures}
            onChange={(e) => onMaxFeaturesChange(Number(e.target.value))}
            className="w-32"
          />
          <div className="text-xs text-muted-foreground">
            {maxFeatures} feature{maxFeatures !== 1 ? 's' : ''}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 