import { useState, useRef } from 'react';
import {
  Card,
  CardContent,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { FileUpload, FileUploadRef } from './file-upload';
import { DataPreviewTable } from './data-preview-table';
import { DimensionSelector } from './dimension-selector';
import { TargetMetricSelector } from './target-metric-selector';
import { MaxFeaturesInput } from './max-features-input';
import { toast } from "sonner";
import { hotspotApiEndpoint } from '@/config';

interface DataPreview {
  headers: string[];
  rows: string[][];
}

export function QuickCreatePanel() {
  const fileUploadRef = useRef<FileUploadRef>(null);
  const [file, setFile] = useState<File | null>(null);
  const [dataPreview, setDataPreview] = useState<DataPreview | null>(null);
  const [selectedDimensions, setSelectedDimensions] = useState<string[]>([]);
  const [targetMetric, setTargetMetric] = useState<{ column: string; value: string }>({ column: '', value: '' });
  const [maxFeatures, setMaxFeatures] = useState<number>(10);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fileKey, setFileKey] = useState<string | null>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !file.name.toLowerCase().endsWith('.csv')) {
      toast.error("Invalid file format", {
        description: "Please upload a CSV file",
      });
      return;
    }
    setFile(file);

    try {
      const chunk = await file.slice(0,10000).text();
      const lines = chunk.split('\n');
      const headers = lines[0].split(',').map(header => header.trim());
      const previewRows = lines.slice(1, 11).map(line =>
        line.split(',').map(cell => cell.trim())
      );
      
      setDataPreview({ headers, rows: previewRows });
    } catch (error) {
      console.error('Error parsing CSV:', error);
      toast.error("CSV parsing error", {
        description: "The uploaded file could not be parsed. Please ensure it's a valid CSV format."
      });
    }
  };

  const handleUploadComplete = (key: string) => {
    toast.success("File uploaded successfully", {
      description: "Your dataset has been uploaded."
    });


    setFileKey(key);
  };

  const handleUploadError = (error: string) => {
    toast.error("Upload failed", {
      description: error
    });
    setIsSubmitting(false);
  };

  const createAnalysisJob = async (fileKey: string) => {
    try {
      const toastId = toast.loading("Creating analysis job...");

      const response = await fetch(`${hotspotApiEndpoint}/analysis-jobs/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          datasetKey: fileKey,
          targetAttribute: targetMetric.column,
          targetValue: targetMetric.value,
          maxFeatures: maxFeatures,
          minSubgroupSize: 30,
          useParallel: true,
          dimensions: selectedDimensions,
          jobName: `Analysis-${new Date().toISOString()}`
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create analysis job: ${errorText}`);
      }

      const result = await response.json();

      toast.success("Analysis job created", {
        id: toastId,
        description: `Job ID: ${result.jobId}. Status: ${result.status}`
      });

      console.log('Analysis job created:', result);
      return result.jobId;
    } catch (error) {
      console.error('Error creating analysis job:', error);
      toast.error("Failed to create analysis job", {
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) return;

    setIsSubmitting(true);

    try {

      if (fileKey) {
        await createAnalysisJob(fileKey);
      } else {

        if (fileUploadRef.current) {
          const key = await fileUploadRef.current.uploadFile(file);
          if (key) {
            setFileKey(key);
            await createAnalysisJob(key);
          }
        } else {
          toast.error("File upload component not initialized");
        }
      }
    } catch (error) {
      console.error('Error during form submission:', error);
      toast.error("Error creating analysis", {
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isSubmitDisabled = !file || selectedDimensions.length === 0 || !targetMetric.column || !targetMetric.value || isSubmitting;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardContent className="p-4">
          <ResizablePanelGroup
            direction="horizontal"
            className="min-h-[550px] rounded-lg border"
          >
            {/* Left panel - File upload and preview */}
            <ResizablePanel defaultSize={50} minSize={30}>
              <div className="flex flex-col h-full p-4 space-y-4">
                <FileUpload
                  ref={fileUploadRef}
                  onFileUpload={handleFileUpload}
                  onUploadComplete={handleUploadComplete}
                  onUploadError={handleUploadError}
                />

                {dataPreview && (
                  <div className="flex-1 overflow-hidden">
                    <DataPreviewTable dataPreview={dataPreview} />
                  </div>
                )}
              </div>
            </ResizablePanel>

            <ResizableHandle withHandle />

            {/* Right panel - Configuration */}
            <ResizablePanel defaultSize={50} minSize={30}>
              <div className="flex flex-col h-full p-4 space-y-4 overflow-y-auto">
                {dataPreview ? (
                  <>
                    <DimensionSelector
                      headers={dataPreview.headers}
                      selectedDimensions={selectedDimensions}
                      onDimensionChange={setSelectedDimensions}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                      <div className="md:col-span-3">
                        <TargetMetricSelector
                          headers={dataPreview.headers}
                          targetMetric={targetMetric}
                          onTargetMetricChange={setTargetMetric}
                        />
                      </div>

                      <div className="md:col-span-2">
                        <MaxFeaturesInput
                          maxFeatures={maxFeatures}
                          onMaxFeaturesChange={setMaxFeatures}
                        />
                      </div>
                    </div>

                    <div className="mt-4">
                      <Button
                        type="submit"
                        className="w-full"
                        disabled={isSubmitDisabled}
                      >
                        {isSubmitting ? "Creating Analysis..." : "Create Analysis"}
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    Upload a dataset to configure analysis options
                  </div>
                )}
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </CardContent>
      </Card>
    </form>
  );
}