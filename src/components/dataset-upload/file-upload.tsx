import { useState, forwardRef, useImperativeHandle } from 'react';
import { UploadIcon } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { hotspotApiEndpoint } from '@/config';
import { toast } from "sonner";

interface FileUploadProps {
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onUploadComplete?: (key: string) => void;
  onUploadError?: (error: string) => void;
  showFilePreview?: boolean;
}

export type UploadStatus = {
  uploading: boolean;
  progress: number;
  error: string | null;
};

export interface FileUploadRef {
  uploadFile: (file: File) => Promise<string | null>;
}

export const FileUpload = forwardRef<FileUploadRef, FileUploadProps>(
  ({ onFileUpload, onUploadComplete, onUploadError, showFilePreview = true }, ref) => {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [uploadStatus, setUploadStatus] = useState<UploadStatus>({
      uploading: false,
      progress: 0,
      error: null
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        setSelectedFile(file);
        setUploadStatus(prev => ({ ...prev, error: null }));

        onFileUpload(event);
      }
    };

    const uploadFile = async (file: File): Promise<string | null> => {
      if (!file) return null;

      try {
        setUploadStatus({
          uploading: true,
          progress: 0,
          error: null
        });


        const toastId = toast.loading("Uploading file...", {
          description: "0%"
        });


        const presignedUrlResponse = await fetch(hotspotApiEndpoint + '/upload-url', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fileName: file.name,
            fileType: 'text/csv',
          }),
        });

        if (!presignedUrlResponse.ok) {
          throw new Error('Failed to get presigned URL');
        }

        const { presignedUrl, key } = await presignedUrlResponse.json();


        await new Promise<void>((resolve, reject) => {
          const xhr = new XMLHttpRequest();


          xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
              const percentCompleted = Math.round((event.loaded * 100) / event.total);
              setUploadStatus(prev => ({ ...prev, progress: percentCompleted }));


              toast.loading("Uploading file...", {
                id: toastId,
                description: `${percentCompleted}%`
              });
            }
          });


          xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              resolve();
            } else {
              reject(new Error(`Upload failed with status: ${xhr.status}`));
            }
          });


          xhr.addEventListener('error', () => {
            reject(new Error('Upload failed due to network error'));
          });


          xhr.open('PUT', presignedUrl);
          xhr.setRequestHeader('Content-Type', 'text/csv');


          xhr.send(file);
        });


        setUploadStatus(prev => ({ ...prev, progress: 100 }));
        toast.success("File uploaded successfully", {
          id: toastId,
          description: `${file.name} has been uploaded`
        });
        console.log('File uploaded successfully', { key });

        if (onUploadComplete) {
          onUploadComplete(key);
        }

        return key;
      } catch (error) {
        console.error('Error uploading file:', error);
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';
        setUploadStatus(prev => ({ ...prev, error: errorMessage }));

        toast.error("Upload failed", {
          description: errorMessage
        });

        if (onUploadError) {
          onUploadError(errorMessage);
        }

        return null;
      } finally {
        setUploadStatus(prev => ({ ...prev, uploading: false }));
      }
    };


    useImperativeHandle(ref, () => ({
      uploadFile
    }));


    if (selectedFile && !showFilePreview) {

      return (
        <Input
          id="file-upload"
          type="file"
          accept=".csv"
          onChange={handleFileChange}
          className="cursor-pointer"
        />
      );
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle>Upload Dataset</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col space-y-2">
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Input
                    id="file-upload"
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    className="cursor-pointer"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Only CSV files are supported
                  </p>
                </div>
              </div>
            </div>

            {uploadStatus.uploading && (
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span>Uploading...</span>
                  <span>{uploadStatus.progress}%</span>
                </div>
                <Progress value={uploadStatus.progress} className="h-2" />
              </div>
            )}

            {selectedFile && (
              <div className="p-2 bg-muted rounded flex items-center gap-2">
                <UploadIcon className="h-4 w-4 text-muted-foreground" />
                <div className="text-sm flex-1 truncate">
                  {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }
); 