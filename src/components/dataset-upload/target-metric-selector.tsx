import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface TargetMetric {
  column: string;
  value: string;
}

interface TargetMetricSelectorProps {
  headers: string[];
  targetMetric: TargetMetric;
  onTargetMetricChange: (targetMetric: TargetMetric) => void;
  className?: string;
}

export function TargetMetricSelector({
  headers,
  targetMetric,
  onTargetMetricChange,
  className = ""
}: TargetMetricSelectorProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <CardTitle className="text-sm">Target Metric</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-x-4 gap-y-2">
          <div className="col-span-1">
            <Label htmlFor="target-column" className="text-xs text-muted-foreground mb-1 block">
              Column
            </Label>
            <Select
              value={targetMetric.column}
              onValueChange={(value) => onTargetMetricChange({ ...targetMetric, column: value })}
            >
              <SelectTrigger id="target-column" className="w-full">
                <SelectValue placeholder="Select Column" />
              </SelectTrigger>
              <SelectContent>
                {headers.map((header) => (
                  <SelectItem key={header} value={header}>
                    {header}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="col-span-1">
            <Label htmlFor="target-value" className="text-xs text-muted-foreground mb-1 block">
              Target Value
            </Label>
            <Input
              id="target-value"
              type="text"
              placeholder="Target Value"
              value={targetMetric.value}
              onChange={(e) => onTargetMetricChange({ ...targetMetric, value: e.target.value })}
              disabled={!targetMetric.column}
              className="w-full"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 