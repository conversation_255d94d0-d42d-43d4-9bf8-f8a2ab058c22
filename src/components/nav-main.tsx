"use client"

import { type LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { DashboardView } from "@/dashboard/dashboard"

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

interface NavItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  primary?: boolean;
  linkedPanel?: string;
  view?: DashboardView;
  [key: string]: any;
}

interface NavMainProps {
  items: NavItem[];
  onItemClick?: (linkedPanel?: string, item?: NavItem) => void;
}

export function NavMain({ items, onItemClick }: NavMainProps) {
  const handleClick = (item: NavItem, e: React.MouseEvent) => {

    if (onItemClick) {
      onItemClick(item.linkedPanel, item);
    }


    if (item.url.startsWith('#')) {
      e.preventDefault();
    }
  };

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem key={item.title} className={item.primary ? "flex items-center gap-2" : ""}>
              <SidebarMenuButton
                tooltip={item.title}
                asChild
                className={cn(
                  item.primary && "min-w-8 bg-primary text-primary-foreground duration-200 ease-linear hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground"
                )}
              >
                <a
                  href={item.url}
                  onClick={(e) => handleClick(item, e)}
                >
                  {item.icon && <item.icon />}
                  <span>{item.title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
