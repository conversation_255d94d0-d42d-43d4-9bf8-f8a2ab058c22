#!/usr/bin/env python3.11
from typing import Union, ClassVar
import sys
import json
import time
import logging
import argparse
from datetime import datetime
import pandas as pd
import boto3
import ray
import textwrap
import colorsys
from graphviz import Digraph
from botocore.exceptions import ClientError
from pandas import DataFrame
from pandas.api.types import is_string_dtype
from subgroups.algorithms.algorithm import Algorithm
from subgroups.quality_measures.quality_measure import QualityMeasure
from subgroups.exceptions import DatasetAttributeTypeError
from subgroups.data_structures.vertical_list import VerticalList
from subgroups.data_structures.vertical_list_with_bitsets import VerticalListWithBitsets
from subgroups.data_structures.vertical_list_with_sets import VerticalListWithSets
from subgroups.core.pattern import Pattern
from subgroups.core.operator import Operator
from subgroups.core.selector import Selector
from subgroups.core.subgroup import Subgroup
from subgroups.quality_measures import WRAcc
from subgroups.quality_measures import WRAccOptimisticEstimate1
from concurrent.futures import ThreadPoolExecutor  # Required for parallel processing
import multiprocessing  # Required to get the number of CPU cores
from threading import Lock  # Required for thread-safe operations

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def _delete_subgroup_parameters_from_a_dictionary(dict_of_parameters: dict[str, Union[int, float]]):
    """Private method to delete the subgroup parameters (i.e., tp, fp, TP and FP) from a dictionary of parameters. IMPORTANT: this method modifies the parameter, does not return a new dictionary.

    :param dict_of_parameters: the dictionary of parameters which is modified.
    """
    try:
        del dict_of_parameters[QualityMeasure.TRUE_POSITIVES]
    except KeyError:
        pass
    try:
        del dict_of_parameters[QualityMeasure.FALSE_POSITIVES]
    except KeyError:
        pass
    try:
        del dict_of_parameters[QualityMeasure.TRUE_POPULATION]
    except KeyError:
        pass
    try:
        del dict_of_parameters[QualityMeasure.FALSE_POPULATION]
    except KeyError:
        pass


def _query_triangular_matrix(matrix: dict[Selector, dict[Selector, VerticalList]], index_a: Selector, index_b: Selector) -> Union[VerticalList, None]:
    """Private method to query a triangular matrix.

    :param matrix: the triangular matrix which is queried.
    :param index_a: the first index in the query.
    :param index_b: the second index in the query.
    :return: either the Vertical List contained in matrix[index_a][index_b], or the Vertical List contained in matrix[index_b][index_a] if the firt one was None, or None if they were both None.
    """
    try:
        return matrix[index_a][index_b]
    except KeyError:
        try:
            return matrix[index_b][index_a]
        except KeyError:
            return None


class VLSD(Algorithm):
    """This class represents the VLSD algorithm.

    :param quality_measure: the quality measure which is used.
    :param q_minimum_threshold: the minimum quality threshold for the quality measure.
    :param optimistic_estimate: the optimistic estimate of the quality measure which is used.
    :param oe_minimum_threshold: the minimum quality threshold for the optimistic estimate.
    :param additional_parameters_for_the_quality_measure: if the quality measure passed by parameter needs more parameters apart from tp, fp, TP and FP to be computed, they need to be specified here.
    :param additional_parameters_for_the_optimistic_estimate: if the optimistic estimate passed by parameter needs more parameters apart from tp, fp, TP and FP to be computed, they need to be specified here.
    :param sort_criterion_in_s1: the criterion to use in order to sort the Vertical Lists with only one selector. Three values are possible: "quality-ascending" (sort ascending by quality value), "quality-descending" (sort descending by quality value), and "no-order" (do not sort and maintain the generation order). By default, "no-order".
    :param sort_criterion_in_other_sizes: the criterion to use in order to sort the Vertical Lists with more than one selector. Three values are possible: "quality-ascending" (sort ascending by quality value), "quality-descending" (sort descending by quality value), and "no-order" (do not sort and maintain the generation order). By default, "no-order".
    :param write_results_in_file: whether the results obtained will be written in a file. By default, False.
    :param file_path: if 'write_results_in_file' is True, path of the file in which the results will be written.
    :param max_features: maximum number of features to include in subgroup discovery.
    """

    SORT_CRITERION_QUALITY_ASCENDING: ClassVar[str] = "quality-ascending"
    SORT_CRITERION_QUALITY_DESCENDING: ClassVar[str] = "quality-descending"
    SORT_CRITERION_NO_ORDER: ClassVar[str] = "no-order"
    SORT_CRITERION: ClassVar[list[str]] = [
        SORT_CRITERION_QUALITY_ASCENDING, SORT_CRITERION_QUALITY_DESCENDING, SORT_CRITERION_NO_ORDER]

    VERTICAL_LISTS_WITH_BITSETS: ClassVar[str] = "bitsets"
    VERTICAL_LISTS_WITH_SETS: ClassVar[str] = "sets"
    VERTICAL_LISTS_IMPLEMENTATION: ClassVar[list[str]] = [
        VERTICAL_LISTS_WITH_BITSETS, VERTICAL_LISTS_WITH_SETS]

    __slots__ = ("_quality_measure", "_q_minimum_threshold", "_optimistic_estimate", "_oe_minimum_threshold", "_additional_parameters_for_the_quality_measure", "_additional_parameters_for_the_optimistic_estimate",
                 "_unselected_subgroups", "_selected_subgroups", "_sort_criterion_in_s1", "_sort_criterion_in_other_sizes", "_vertical_lists_implementation", "_file_path", "_file", "_max_features")

    def __init__(self, quality_measure: QualityMeasure, q_minimum_threshold: Union[int, float], optimistic_estimate: QualityMeasure, oe_minimum_threshold: Union[int, float], additional_parameters_for_the_quality_measure: dict[str, Union[int, float]] = dict(), additional_parameters_for_the_optimistic_estimate: dict[str, Union[int, float]] = dict(), sort_criterion_in_s1: str = SORT_CRITERION_NO_ORDER, sort_criterion_in_other_sizes: str = SORT_CRITERION_NO_ORDER, vertical_lists_implementation: str = VERTICAL_LISTS_WITH_BITSETS, write_results_in_file: bool = False, file_path: Union[str, None] = None, max_features: int = None) -> None:
        if not isinstance(quality_measure, QualityMeasure):
            raise TypeError(
                "The parameter 'quality_measure' must be an instance of a subclass of the 'QualityMeasure' class.")
        if (type(q_minimum_threshold) is not int) and (type(q_minimum_threshold) is not float):
            raise TypeError(
                "The type of the parameter 'q_minimum_threshold' must be 'int' or 'float'.")
        if not isinstance(optimistic_estimate, QualityMeasure):
            raise TypeError(
                "The parameter 'optimistic_estimate' must be an instance of a subclass of the 'QualityMeasure' class.")
        # We check whether 'optimistic_estimate' is an optimistic estimate of 'quality_measure'.
        if quality_measure.get_name() not in optimistic_estimate.optimistic_estimate_of():
            raise ValueError("The quality measure " + optimistic_estimate.get_name() +
                             " is not an optimistic estimate of the quality measure " + quality_measure.get_name() + ".")
        if (type(oe_minimum_threshold) is not int) and (type(oe_minimum_threshold) is not float):
            raise TypeError(
                "The type of the parameter 'oe_minimum_threshold' must be 'int' or 'float'.")
        if (type(additional_parameters_for_the_quality_measure) is not dict):
            raise TypeError(
                "The type of the parameter 'additional_parameters_for_the_quality_measure' must be 'dict'")
        if (type(additional_parameters_for_the_optimistic_estimate) is not dict):
            raise TypeError(
                "The type of the parameter 'additional_parameters_for_the_optimistic_estimate' must be 'dict'")
        if (sort_criterion_in_s1 not in VLSD.SORT_CRITERION):
            raise ValueError(
                "The value of the parameter 'sort_criterion_in_s1' is not valid. See the documentation.")
        if (sort_criterion_in_other_sizes not in VLSD.SORT_CRITERION):
            raise ValueError(
                "The value of the parameter 'sort_criterion_in_other_sizes' is not valid. See the documentation.")
        if (vertical_lists_implementation not in VLSD.VERTICAL_LISTS_IMPLEMENTATION):
            raise ValueError(
                "The value of the parameter 'vertical_lists_implementation' is not valid. See the documentation.")
        if (type(write_results_in_file) is not bool):
            raise TypeError(
                "The type of the parameter 'write_results_in_file' must be 'bool'")
        if ((type(file_path) is not str) and (file_path is not None)):
            raise TypeError(
                "The type of the parameter 'file_path' must be 'str' or 'NoneType'.")
        # If 'write_results_in_file' is True, 'file_path' must not be None.
        if (write_results_in_file) and (file_path is None):
            raise ValueError(
                "If the parameter 'write_results_in_file' is True, the parameter 'file_path' must not be None.")
        self._quality_measure = quality_measure
        self._q_minimum_threshold = q_minimum_threshold
        self._optimistic_estimate = optimistic_estimate
        self._oe_minimum_threshold = oe_minimum_threshold
        self._additional_parameters_for_the_quality_measure = additional_parameters_for_the_quality_measure.copy()
        _delete_subgroup_parameters_from_a_dictionary(
            self._additional_parameters_for_the_quality_measure)
        self._additional_parameters_for_the_optimistic_estimate = additional_parameters_for_the_optimistic_estimate.copy()
        _delete_subgroup_parameters_from_a_dictionary(
            self._additional_parameters_for_the_optimistic_estimate)
        self._unselected_subgroups = 0
        self._selected_subgroups = 0
        self._sort_criterion_in_s1 = sort_criterion_in_s1
        self._sort_criterion_in_other_sizes = sort_criterion_in_other_sizes
        self._vertical_lists_implementation = vertical_lists_implementation
        if (write_results_in_file):
            self._file_path = file_path
        else:
            self._file_path = None
        self._file = None
        self._max_features = max_features

    def _get_quality_measure(self) -> QualityMeasure:
        return self._quality_measure

    def _get_q_minimum_threshold(self) -> Union[int, float]:
        return self._q_minimum_threshold

    def _get_optimistic_estimate(self) -> QualityMeasure:
        return self._optimistic_estimate

    def _get_oe_minimum_threshold(self) -> Union[int, float]:
        return self._oe_minimum_threshold

    def _get_additional_parameters_for_the_quality_measure(self) -> dict[str, Union[int, float]]:
        return self._additional_parameters_for_the_quality_measure

    def _get_additional_parameters_for_the_optimistic_estimate(self) -> dict[str, Union[int, float]]:
        return self._additional_parameters_for_the_optimistic_estimate

    quality_measure = property(
        _get_quality_measure, None, None, "The quality measure which is used.")
    q_minimum_threshold = property(_get_q_minimum_threshold, None,
                                   None, "The minimum quality threshold for the quality measure.")
    optimistic_estimate = property(_get_optimistic_estimate, None, None,
                                   "The optimistic estimate of the quality measure which is used.")
    oe_minimum_threshold = property(_get_oe_minimum_threshold, None,
                                    None, "The minimum quality threshold for the optimistic estimate.")
    additional_parameters_for_the_quality_measure = property(
        _get_additional_parameters_for_the_quality_measure, None, None, "The additional needed parameters with which to compute the quality measure.")
    additional_parameters_for_the_optimistic_estimate = property(
        _get_additional_parameters_for_the_optimistic_estimate, None, None, "The additional needed parameters with which to compute the optimistic estimate.")

    def _get_unselected_subgroups(self) -> int:
        return self._unselected_subgroups

    def _get_selected_subgroups(self) -> int:
        return self._selected_subgroups

    def _get_visited_nodes(self) -> int:
        return self._unselected_subgroups + self._selected_subgroups

    unselected_subgroups = property(_get_unselected_subgroups, None, None,
                                    "Number of unselected subgroups after executing the VLSD algorithm (before executing the 'fit' method, this attribute is 0).")
    selected_subgroups = property(_get_selected_subgroups, None, None,
                                  "Number of selected subgroups after executing the VLSD algorithm (before executing the 'fit' method, this attribute is 0).")
    visited_nodes = property(_get_visited_nodes, None, None,
                             "Number of visited nodes after executing the VLSD algorithm (before executing the 'fit' method, this attribute is 0).")

    def _get_sort_criterion_in_s1(self) -> str:
        return self._sort_criterion_in_s1

    def _get_sort_criterion_in_other_sizes(self) -> str:
        return self._sort_criterion_in_other_sizes

    sort_criterion_in_s1 = property(_get_sort_criterion_in_s1, None, None,
                                    "The criterion to use in order to sort the Vertical Lists with only one selector.")
    sort_criterion_in_other_sizes = property(_get_sort_criterion_in_other_sizes, None, None,
                                             "The criterion to use in order to sort the Vertical Lists with more than one selector.")

    def _handle_individual_result(self, individual_result: tuple[VerticalList, tuple[str, str], int, int]) -> dict:
        """Private method to handle each individual result generated by the VLSD algorithm.

        :param individual_result: the individual result which is handled. In this case, it is a Vertical List, a target as a tuple and the subgroup parameters TP and FP.
        :return: Dictionary with subgroup information if quality is sufficient, None otherwise
        """
        # Get the subgroup parameters.
        tp = individual_result[0].tp
        fp = individual_result[0].fp
        TP = individual_result[2]
        FP = individual_result[3]

        # Compute the quality measure of the frequent pattern along with the target
        dict_of_parameters = {QualityMeasure.TRUE_POSITIVES: tp, QualityMeasure.FALSE_POSITIVES: fp,
                              QualityMeasure.TRUE_POPULATION: TP, QualityMeasure.FALSE_POPULATION: FP}
        dict_of_parameters.update(
            self._additional_parameters_for_the_quality_measure)
        quality_measure_value = self._quality_measure.compute(
            dict_of_parameters)

        # Add the subgroup only if the quality measure value is greater or equal than the threshold.
        if quality_measure_value >= self._q_minimum_threshold:
            # Check if the subgroup exceeds the max_features limit
            if self._max_features is not None:
                current_size = len(individual_result[0].list_of_selectors)
                if current_size > self._max_features:
                    return None  # Skip this subgroup if it exceeds max_features
            # Increment the number of selected subgroups.
            self._selected_subgroups = self._selected_subgroups + 1

            # Get the description
            subgroup_description = Pattern(
                individual_result[0].list_of_selectors)
            # Attribute name -> target_as_tuple[0], Attribute value -> target_as_tuple[1]
            target_as_tuple = individual_result[1]

            # Create the subgroup info
            subgroup = Subgroup(subgroup_description, Selector(
                target_as_tuple[0], Operator.EQUAL, target_as_tuple[1]))
            subgroup_dict = {
                'description': str(subgroup),
                'target': target_as_tuple[1],
                'pattern_length': len(individual_result[0].list_of_selectors),
                'quality': quality_measure_value,
                'urr_rate': tp / (tp + fp) if (tp + fp) > 0 else 0,
                'impact_radius_relative': (tp / TP * 100) if TP > 0 else 0,
                'tp': tp,
                'fp': fp,
                'TP': TP,
                'FP': FP
            }
            # Save incrementally to S3 if output path is specified
            if self._file_path and self._file_path.startswith('s3://') and hasattr(self, '_file_path'):
                self._append_to_s3(subgroup_dict)
            return subgroup_dict
        else:
            # Increment the number of unselected subgroups.
            self._unselected_subgroups = self._unselected_subgroups + 1
            return None

    def _append_to_s3(self, subgroup_dict: dict):
        """Append a single subgroup to the results file in S3."""
        s3_client = boto3.client('s3')
        bucket, key = self._file_path.replace("s3://", "").split("/", 1)

        try:
            # Fetch existing results from S3
            existing_content = {'subgroups': []}
            try:
                response = s3_client.get_object(Bucket=bucket, Key=key)
                existing_content = json.loads(response['Body'].read().decode('utf-8'))
            except s3_client.exceptions.NoSuchKey:
                # If the file doesn't exist, initialize with an empty structure
                logger.info(f"Results file not found at s3://{bucket}/{key}, initializing new file.")

            # Append the new subgroup
            existing_content['subgroups'].append(subgroup_dict)

            # Upload the updated content back to S3
            s3_client.put_object(
                Bucket=bucket,
                Key=key,
                Body=json.dumps(existing_content, indent=2),
                ContentType='application/json'
            )
            logger.info(f"Appended subgroup to S3 file: s3://{bucket}/{key}")
        except Exception as e:
            logger.error(f"Error appending subgroup to S3: {e}")
    # IMPORTANT: although the subgroup parameters TP and FP can be computed from 'pandas_dataframe', we also pass them by parameter in this method to avoid computing them twice (in the 'fit' method and in this method).
    def _generate_subgroups_s1(self, pandas_dataframe: DataFrame, target: tuple[str, str], TP: int, FP: int) -> list[VerticalList]:
        """Private method to generate the list of Vertical Lists of size 1 (i.e., whose list of selectors has only one selector), prune it and sort it.

        :param pandas_dataframe: the DataFrame which is scanned. This algorithm only supports nominal attributes (i.e., type 'str') without missing values.
        :param target: a tuple with 2 elements: the target attribute name and the target value.
        :param TP: the true population of the dataset. IMPORTANT: although it can be computed from 'pandas_dataframe', we pass it by parameter to avoid computing it twice (in the 'fit' method and in this method).
        :param FP: the false population of the dataset. IMPORTANT: although it can be computed from 'pandas_dataframe', we pass it by parameter to avoid computing it twice (in the 'fit' method and in this method).
        :return: a list in which each element is a Vertical List of size 1 (i.e., it only has one selector in its list of selectors). The list is pruned according to the threshold and sorted according to 'sort_criterion_in_s1' attribute.
        """
        # Get the target column as a mask: True if the value is equal to the target value and False otherwise.
        target_attribute_as_a_mask = (pandas_dataframe[target[0]] == target[1])
        # Result.
        result = []
        # Iterate through the columns (except the target).
        for column in pandas_dataframe.columns.drop(target[0]):
            # Use the 'groupby' method in order to group each value depending on whether appears with the target or not.
            # - In this case, the property 'indices' is a dictionary in which the key is the tuple "(value, value)" and the
            #   value is a sequence of register indices in which that combination appears.
            values_and_target_grouped = pandas_dataframe.groupby(
                [column, target_attribute_as_a_mask]).indices
            # Set of values which have been already processed.
            processed_values = set()
            # Iterate through the tuples returned by the groupby method.
            for value_target_tuple in values_and_target_grouped:
                value = value_target_tuple[0]
                # Process the tuple only if the value was not seen before.
                if value not in processed_values:
                    # Registers which have the target.
                    try:
                        registers_tp = values_and_target_grouped[(value, True)]
                    except KeyError:
                        registers_tp = []  # Empty sequence.
                    # Registers which do not have the target.
                    try:
                        registers_fp = values_and_target_grouped[(
                            value, False)]
                    except KeyError:
                        registers_fp = []  # Empty sequence.
                    # Compute the optimistic estimate.
                    dict_of_parameters = {QualityMeasure.TRUE_POSITIVES: len(registers_tp), QualityMeasure.FALSE_POSITIVES: len(
                        registers_fp), QualityMeasure.TRUE_POPULATION: TP, QualityMeasure.FALSE_POPULATION: FP}
                    dict_of_parameters.update(
                        self._additional_parameters_for_the_optimistic_estimate)
                    optimistic_estimate_value = self._optimistic_estimate.compute(
                        dict_of_parameters)
                    # Pruning: add the Vertical List only if the optimistic estimate value is greater or equal than the threshold.
                    if optimistic_estimate_value >= self._oe_minimum_threshold:
                        # Create the Vertical List (depending on the specified implementation).
                        vl = None
                        if (self._vertical_lists_implementation == VLSD.VERTICAL_LISTS_WITH_BITSETS):
                            vl = VerticalListWithBitsets([Selector(
                                column, Operator.EQUAL, value)], registers_tp, registers_fp, TP+FP, optimistic_estimate_value)
                        elif (self._vertical_lists_implementation == VLSD.VERTICAL_LISTS_WITH_SETS):
                            vl = VerticalListWithSets([Selector(
                                column, Operator.EQUAL, value)], registers_tp, registers_fp, TP+FP, optimistic_estimate_value)
                        # Add it to the final list.
                        result.append(vl)
                    # Finally, add the value to 'processed_values'.
                    processed_values.add(value)
        # Sort by quality value (optimistic_estimate_value) according to 'sort_criterion_in_s1'.
        if (self._sort_criterion_in_s1 == VLSD.SORT_CRITERION_QUALITY_ASCENDING):
            result.sort(reverse=False, key=lambda x: x.quality_value)
        elif (self._sort_criterion_in_s1 == VLSD.SORT_CRITERION_QUALITY_DESCENDING):
            result.sort(reverse=True, key=lambda x: x.quality_value)
        # Return the list.
        return result

    def _search(self, P: list[VerticalList], M: dict[Selector, dict[Selector, VerticalList]], target: tuple[str, str], TP: int, FP: int, max_features: int) -> list[dict]:
        """Private search method that uses parallel processing.

        :param P: a list of Vertical Lists.
        :param M: the 2-dimensional matrix M (in this case, it is a python dictionary).
        :param target: a tuple with 2 elements: the target attribute name and the target value.
        :param TP: the true population of the dataset.
        :param FP: the false population of the dataset.
        :param max_features: maximum number of features to find
        :return: List of identified subgroups
        """
        file_lock = Lock()
        all_results = []  # Collect all identified subgroups

        def process_pattern(idx):
            """Process a single pattern in parallel."""
            try:
                if idx >= len(P) - 1:
                    return []
                s_x = P[idx]
                if s_x is None:  # Skip None values
                    return []
                s_x_last_selector = s_x.list_of_selectors[-1]
                V = []

                # Process combinations
                for index_y in range(idx + 1, len(P)):
                    s_y = P[index_y]
                    if s_y is None:  # Skip None values
                        continue
                    s_y_last_selector = s_y.list_of_selectors[-1]
                    vertical_list_in_M = _query_triangular_matrix(M, s_x_last_selector, s_y_last_selector)
                    if (vertical_list_in_M is not None) and (vertical_list_in_M.quality_value >= self.oe_minimum_threshold):
                        s_xy_dict_of_parameters = {QualityMeasure.TRUE_POPULATION: TP, QualityMeasure.FALSE_POPULATION: FP}
                        s_xy_dict_of_parameters.update(self._additional_parameters_for_the_optimistic_estimate)
                        s_xy = s_x.join(s_y, self._optimistic_estimate, s_xy_dict_of_parameters, return_None_if_n_is_0=True)
                        # Check if the subgroup exceeds the max_features limit
                        if self._max_features is not None and len(s_xy.list_of_selectors) > self._max_features:
                            continue  # Skip this subgroup if it exceeds max_features
                        if (s_xy is not None) and (s_xy.quality_value >= self.oe_minimum_threshold):
                            V.append(s_xy)
                            with file_lock:
                                result = self._handle_individual_result((s_xy, target, TP, FP))
                                if result:
                                    all_results.append(result)  # Collect the result
                return V
            except Exception as e:
                logger.error(f"Error processing pattern {idx}: {str(e)}")
                return []

        try:
            if len(P) > 1000:
                # Get number of CPU cores
                n_cores = multiprocessing.cpu_count()

                # Process patterns in parallel
                all_V = []
                with ThreadPoolExecutor(max_workers=n_cores) as executor:
                    futures = []
                    for i in range(len(P) - 1):
                        futures.append(executor.submit(process_pattern, i))
                    # Collect results
                    for future in futures:
                        V = future.result()
                        if V:
                            all_V.extend(V)
                if all_V:
                    if (self._sort_criterion_in_other_sizes == VLSD.SORT_CRITERION_QUALITY_ASCENDING):
                        all_V.sort(reverse=False, key=lambda x: x.quality_value)
                    elif (self._sort_criterion_in_other_sizes == VLSD.SORT_CRITERION_QUALITY_DESCENDING):
                        all_V.sort(reverse=True, key=lambda x: x.quality_value)
                    all_results.extend(self._search(all_V, M, target, TP, FP, max_features))
            else:
                # Fallback to sequential processing for smaller lists
                index_x = 0
                while index_x < (len(P) - 1):
                    s_x = P[index_x]
                    P[index_x] = None
                    index_x += 1
                    s_x_last_selector = s_x.list_of_selectors[-1]
                    V = []
                    for index_y in range(index_x, len(P)):
                        s_y = P[index_y]
                        if s_y is None:
                            continue
                        s_y_last_selector = s_y.list_of_selectors[-1]
                        vertical_list_in_M = _query_triangular_matrix(M, s_x_last_selector, s_y_last_selector)
                        if (vertical_list_in_M is not None) and (vertical_list_in_M.quality_value >= self.oe_minimum_threshold):
                            s_xy_dict_of_parameters = {QualityMeasure.TRUE_POPULATION: TP, QualityMeasure.FALSE_POPULATION: FP}
                            s_xy_dict_of_parameters.update(self._additional_parameters_for_the_optimistic_estimate)
                            s_xy = s_x.join(s_y, self._optimistic_estimate, s_xy_dict_of_parameters, return_None_if_n_is_0=True)
                            if (s_xy is not None) and (s_xy.quality_value >= self.oe_minimum_threshold):
                                V.append(s_xy)
                                result = self._handle_individual_result((s_xy, target, TP, FP))
                                if result:
                                    all_results.append(result)  # Collect the result
                    if V:
                        if (self._sort_criterion_in_other_sizes == VLSD.SORT_CRITERION_QUALITY_ASCENDING):
                            V.sort(reverse=False, key=lambda x: x.quality_value)
                        elif (self._sort_criterion_in_other_sizes == VLSD.SORT_CRITERION_QUALITY_DESCENDING):
                            V.sort(reverse=True, key=lambda x: x.quality_value)
                        all_results.extend(self._search(V, M, target, TP, FP, max_features))
        except Exception as e:
            logger.error(f"Error in search method: {str(e)}")

        return all_results  # Return the collected results

    def fit(self, pandas_dataframe: DataFrame, target: tuple[str, str], max_features: int) -> list:
        """Main method to run the VLSD algorithm. This algorithm only supports nominal attributes (i.e., type 'str'). IMPORTANT: missing values are not supported.

        :param pandas_dataframe: the DataFrame which is scanned. This algorithm only supports nominal attributes (i.e., type 'str'). IMPORTANT: missing values are not supported.
        :param target: a tuple with 2 elements: the target attribute name and the target value.
        :return: List of discovered subgroups
        """
        if type(pandas_dataframe) is not DataFrame:
            raise TypeError(
                "The type of the parameter 'pandas_dataframe' must be 'DataFrame'.")
        if type(target) is not tuple:
            raise TypeError(
                "The type of the parameter 'target' must be 'tuple'.")

        target = (target[0], str(target[1]))

        for column in pandas_dataframe.columns:
            if not is_string_dtype(pandas_dataframe[column]):
                raise DatasetAttributeTypeError("Error in attribute '" + str(
                    column) + "'. This algorithm only supports nominal attributes (i.e., type 'str').")

        self._selected_subgroups = 0
        self._unselected_subgroups = 0

        TP = sum(pandas_dataframe[target[0]] == target[1])
        FP = len(pandas_dataframe.index) - TP

        discovered_subgroups = []
        # Set max_features
        self._max_features = max_features
        logger.info(f"Using max_features: {self._max_features}")

        # Get the list of Vertical Lists of size 1
        S1 = self._generate_subgroups_s1(pandas_dataframe, target, TP, FP)
        logger.info(f"Reached in fit method")

        # Handle each individual result from S1
        for s in S1:
            subgroup = self._handle_individual_result((s, target, TP, FP))
            if subgroup:
                discovered_subgroups.append(subgroup)
        logger.info(f"after handle individual result")

        # Create 2-dimensional empty matrix M (in this case, it is a python dictionary).
        M = dict()
        logger.info(f"created M dict")

        # Double iteration through S1.
        for index_x in range(len(S1)):  # From 0 to len(S1)-1.
            s_x = S1[index_x]
            # Get the last selector of s_x. In this point, there is only one.
            s_x_last_selector = s_x.list_of_selectors[-1]
            # IMPORTANT: x < y ==> From x+1 to len(S1)-1.
            for index_y in range(index_x+1, len(S1)):
                s_y = S1[index_y]
                # Get the last selector of s_y. In this point, there is only one.
                s_y_last_selector = s_y.list_of_selectors[-1]
                # Get the quality value of the join of s_x and s_y.
                s_xy_dict_of_parameters = {
                    QualityMeasure.TRUE_POPULATION: TP, QualityMeasure.FALSE_POPULATION: FP}
                s_xy_dict_of_parameters.update(
                    self._additional_parameters_for_the_optimistic_estimate)
                s_xy = s_x.join(s_y, self._optimistic_estimate,
                                s_xy_dict_of_parameters, return_None_if_n_is_0=True)
                # Check whether n (i.e., tp+fp) is 0 or greater than 0 (in this case, 's_xy' will be None) and whether 's_xy' has quality enough.
                if (s_xy is not None) and (s_xy.quality_value >= self._oe_minimum_threshold):
                    # Add to the dictionary.
                    if s_x_last_selector not in M:
                        M[s_x_last_selector] = dict()
                    # ---> IMPORTANT: M[s_x_last_selector][s_y_last_selector] is equal to M[s_y_last_selector][s_x_last_selector], but only one entry is added (to save memory). This will have to be kept in mind later.
                    M[s_x_last_selector][s_y_last_selector] = s_xy
        # Iterate through the Vertical Lists of size 2 and call to search method.
        for index in range(len(S1)-1):  # From 0 to len(S1)-2.
            selector_i = S1[index].list_of_selectors[-1]
            if (selector_i in M):
                # Get all the values (in this case, Vertical Lists) from the corresponding dictionary.
                P = list(M[selector_i].values())
                # Sort by quality value (optimistic_estimate_value) according to 'sort_criterion_in_other_sizes'.
                if (self._sort_criterion_in_other_sizes == VLSD.SORT_CRITERION_QUALITY_ASCENDING):
                    P.sort(reverse=False, key=lambda x: x.quality_value)
                elif (self._sort_criterion_in_other_sizes == VLSD.SORT_CRITERION_QUALITY_DESCENDING):
                    P.sort(reverse=True, key=lambda x: x.quality_value)
                # Handle each individual result from size 2 subgroups
                for s in P:
                    subgroup = self._handle_individual_result(
                        (s, target, TP, FP))
                    if subgroup:
                        discovered_subgroups.append(subgroup)
                # Search for deeper subgroups using Rays parallelization
                more_subgroups = self._search(
                    P, M, target, TP, FP, max_features)
                discovered_subgroups.extend(more_subgroups)
        logger.info(f"fit method completed")

        return discovered_subgroups

class SubgroupGraph:
    def __init__(self, file_path):
        self._file_path = file_path

    def create_and_display_graph(self):
        """Create and display the subgroup discovery graph"""
        df = pd.read_csv(self._file_path)

        dot = Digraph(comment='Subgroup Discovery Graph')
        # Set rankdir to LR to ensure the graph flows from left to right
        dot.attr(rankdir='LR', nodesep='3', ranksep='2')
        dot.attr('node', shape='box', style='filled', margin='0.1')

        total_TP = df['TP'].iloc[0]  # Get total TP (True Population) from first row
        total_FP = df['FP'].iloc[0]  # Get total FP (False Population) from first row
        total_URR = total_TP / (total_TP + total_FP)

        # Ensure pattern length is numeric and sort DataFrame
        # The CSV already has a 'Pattern Length' column, but let's make sure it's numeric
        # and matches our pattern parsing logic
        df['Pattern Length'] = pd.to_numeric(df['Pattern Length'])

        # Verify pattern length matches the actual pattern content
        def extract_pattern_length(pattern_str):
            if '[' in pattern_str and ']' in pattern_str:
                pattern_part = pattern_str.split('[')[1].split(']')[0]
                return len(pattern_part.split(', '))
            return 0

        # Create a new column with the calculated pattern length for verification
        df['Calculated Length'] = df['Pattern'].apply(extract_pattern_length)

        # If there's a mismatch, use the calculated length
        if (df['Pattern Length'] != df['Calculated Length']).any():
            logger.warning("Pattern length mismatch detected in CSV. Using calculated length.")
            df['Pattern Length'] = df['Calculated Length']

        # Drop the temporary column
        df = df.drop('Calculated Length', axis=1)

        # Sort the DataFrame
        df = df.sort_values(['Pattern Length', 'URR Rate'], ascending=[True, False])

        min_urr = df['URR Rate'].min()
        max_urr = df['URR Rate'].max()
        grouped = df.groupby('Pattern Length')

        # Create root node with population information
        root_node_id = "root_node"
        root_label = f"Root\nTrue Population: {total_TP}\nFalse Population: {total_FP}\nTotal URR Rate: {total_URR:.4f}"
        dot.node(root_node_id, root_label, style='filled', fillcolor='#FFFFFF')

        # Create a separate subgraph for each level to enforce hierarchical layout
        max_length = df['Pattern Length'].max()
        for length in range(1, max_length + 1):
            # Create a subgraph for each level with rank=same to align nodes horizontally
            with dot.subgraph(name=f'cluster_level_{length}') as c:
                c.attr(rank='same')
                c.attr(label=f'Level {length}', style='rounded')

                # Only process this level if it exists in the data
                if length in grouped.groups:
                    group = grouped.get_group(length)
                    for index, row in group.iterrows():
                        node_id = f"node_{index}"

                        wrapped_pattern = textwrap.fill(row['Pattern'], width=30)
                        node_label = f"{wrapped_pattern}\n"
                        node_label += f"Quality: {row['Quality Measure']:.4f}\n"
                        node_label += f"URR Rate: {row['URR Rate']:.4f}\n"
                        node_label += f"Impact: {row['Impact Radius (%)']:.2f}%\n"
                        node_label += f"tp: {row['tp']}"

                        # Color nodes based on URR Rate
                        if max_urr == min_urr:
                            hue = 0.3  # Default to a yellowish color if all values are the same
                        else:
                            try:
                                normalized_rate = (row['URR Rate'] - min_urr) / (max_urr - min_urr)
                                hue = 0.33 * (1 - normalized_rate)
                            except (ZeroDivisionError, ValueError):
                                hue = 0.3  # Default to a yellowish color if there's an error

                        rgb = colorsys.hsv_to_rgb(hue, 1.0, 1.0)
                        node_color = '#{:02x}{:02x}{:02x}'.format(int(rgb[0]*255), int(rgb[1]*255), int(rgb[2]*255))

                        dot.node(node_id, node_label, style='filled', fillcolor=node_color)

        # Connect root node to all level 1 nodes
        level_1_nodes = df[df['Pattern Length'] == 1]
        for index, _ in level_1_nodes.iterrows():
            dot.edge(root_node_id, f"node_{index}")

        # Create edges between related subgroups
        # For each pattern length starting from 2
        for length in range(2, max_length + 1):
            current_level_nodes = df[df['Pattern Length'] == length]
            previous_level_nodes = df[df['Pattern Length'] == length - 1]

            # For each node in the current level
            for curr_idx, curr_row in current_level_nodes.iterrows():
                # Extract the pattern parts from the description
                # The pattern is in the format: "Subgroup: [attr1=value1, attr2=value2] → target=value"
                # We need to extract the selectors between the square brackets
                pattern_str = curr_row['Pattern']
                if '[' in pattern_str and ']' in pattern_str:
                    pattern_part = pattern_str.split('[')[1].split(']')[0]
                    current_pattern = set(pattern_part.split(', '))
                else:
                    # Fallback if the pattern doesn't match expected format
                    current_pattern = set()

                # Find parent nodes in the previous level that are subsets
                for prev_idx, prev_row in previous_level_nodes.iterrows():
                    # Extract the pattern parts from the description for the parent
                    pattern_str = prev_row['Pattern']
                    if '[' in pattern_str and ']' in pattern_str:
                        pattern_part = pattern_str.split('[')[1].split(']')[0]
                        parent_pattern = set(pattern_part.split(', '))
                    else:
                        # Fallback if the pattern doesn't match expected format
                        parent_pattern = set()

                    # Only create an edge if the parent pattern is a subset of the current pattern
                    # and the difference is exactly one element (immediate parent)
                    if parent_pattern and current_pattern and parent_pattern.issubset(current_pattern) and len(current_pattern) - len(parent_pattern) == 1:
                        dot.edge(f"node_{prev_idx}", f"node_{curr_idx}")

        return dot

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='Run subgroup discovery on a dataset')
    parser.add_argument('--dataset', required=True,
                        help='S3 path to the dataset (CSV or Parquet)')
    parser.add_argument('--target-attribute', required=True,
                        help='Target attribute name')
    parser.add_argument('--target-value', required=True,
                        help='Target attribute value')
    parser.add_argument('--max-features', type=int, default=10,
                        help='Maximum number of features to include')
    parser.add_argument('--output-path', required=True,
                        help='S3 path for output results')
    parser.add_argument('--job-id', required=True, help='Unique job ID')

    args = parser.parse_args()
    return args


def load_dataset(dataset_path):
    """Load dataset from S3 path"""
    logger.info(f"Loading dataset from {dataset_path}")
    try:
        if dataset_path.lower().endswith('.csv'):
            df = pd.read_csv(dataset_path)
        elif dataset_path.lower().endswith('.parquet'):
            df = pd.read_parquet(dataset_path)
        else:
            raise ValueError(f"Unsupported file format: {dataset_path}")

        logger.info(f"Dataset loaded successfully with shape: {df.shape}")
        # Convert non-string columns to strings
        for column in df.columns:
            if not is_string_dtype(df[column]):
                logger.warning(
                    f"Column '{column}' is not of type 'str'. Converting to 'str'.")
                df[column] = df[column].astype(str)
        return df
    except Exception as e:
        logger.error(f"Failed to load dataset: {e}")
        raise


def run_subgroup_discovery(df, target_attribute, target_value, max_features: int, job_id=None, dataset_path=None):
    """Run the VLSD algorithm on the dataset"""
    logger.info(
        f"Running subgroup discovery with target: {target_attribute}={target_value}")
    quality_measure = WRAcc()
    output_path = f"s3://hotspot-user-data-dev/results/{job_id}"

    vlsd = VLSD(
        quality_measure=quality_measure,
        q_minimum_threshold=0.001,
        optimistic_estimate=WRAccOptimisticEstimate1(),
        oe_minimum_threshold=0.85,
        write_results_in_file=True,
        file_path=f"{output_path}/incremental_results.json",
        max_features=max_features
    )

    start_time = time.time()

    logger.info("Starting subgroup discovery")
    result = vlsd.fit(df, (target_attribute, str(target_value)), max_features)

    end_time = time.time()
    execution_time = end_time - start_time

    logger.info(
        f"Subgroup discovery completed in {execution_time:.2f} seconds")
    logger.info(f"Found {len(result)} subgroups")

    result.sort(key=lambda x: x['quality'], reverse=True)

    results = {
        'jobId': job_id,
        'timestamp': datetime.utcnow().isoformat(),
        'targetAttribute': target_attribute,
        'targetValue': str(target_value),
        'datasetPath': dataset_path,
        'executionTimeSeconds': execution_time,
        'totalSubgroupsFound': len(result),
        'subgroupsReturned': len(result),
        'parallelProcessingUsed': True,
        'subgroups': result,
        'parameters': {
            'maxFeatures': max_features
        }
    }

    return results


def save_results_to_s3(results, output_path, job_id):
    """Save results to S3"""
    logger.info(f"Saving results to {output_path}")

    if not output_path.startswith('s3://'):
        raise ValueError(f"Output path must be an S3 path: {output_path}")

    path_parts = output_path.replace('s3://', '').split('/')
    bucket = path_parts[0]
    job_path = f"results/{job_id}"

    s3_client = boto3.client('s3')

    results_key = f"{job_path}/results.json"
    try:
        s3_client.put_object(
            Bucket=bucket,
            Key=results_key,
            Body=json.dumps(results, indent=2),
            ContentType='application/json'
        )
        logger.info(f"Results saved to s3://{bucket}/{results_key}")

        csv_data = "Pattern,Target,Pattern Length,Quality Measure,URR Rate,Impact Radius (%),tp,fp,TP,FP\n"
        for sg in results['subgroups']:
            csv_data += f"\"{sg['description']}\",{sg['target']},{sg['pattern_length']},{sg['quality']:.4f}," \
                       f"{sg['urr_rate']:.4f},{sg['impact_radius_relative']:.2f},{sg['tp']},{sg['fp']},{sg['TP']},{sg['FP']}\n"

        csv_key = f"{job_path}/top_subgroups.csv"
        s3_client.put_object(
            Bucket=bucket,
            Key=csv_key,
            Body=csv_data,
            ContentType='text/csv'
        )
        logger.info(f"CSV summary saved to s3://{bucket}/{csv_key}")

        # Generate and save the graph
        try:
            # Download the CSV file to a temporary location
            temp_csv_path = f"/tmp/top_subgroups_{job_id}.csv"
            s3_client.download_file(bucket, csv_key, temp_csv_path)

            # Create and generate the graph using the SubgroupGraph class defined in this file
            graph_generator = SubgroupGraph(temp_csv_path)
            dot = graph_generator.create_and_display_graph()

            # Save the graph locally
            graph_path = f"/tmp/subgroup_graph_{job_id}"
            dot.render(graph_path, format='pdf', cleanup=True)

            # Upload the graph to S3
            graph_key = f"{job_path}/subgroup_graph.pdf"
            s3_client.upload_file(
                f"{graph_path}.pdf",
                bucket,
                graph_key,
                ExtraArgs={'ContentType': 'application/pdf'}
            )
            logger.info(f"Graph saved to s3://{bucket}/{graph_key}")

            # Clean up temporary files
            import os
            os.remove(temp_csv_path)
            os.remove(f"{graph_path}.pdf")

        except Exception as e:
            logger.error(f"Error generating or saving graph: {e}")
            # Continue even if graph generation fails
            pass
        return True
    except ClientError as e:
        logger.error(f"Error saving results to S3: {e}")
        return False


if __name__ == "__main__":
    try:
        args = parse_arguments()
        logger.info(f"Running with arguments: {args}")

        # Load the dataset
        df = load_dataset(args.dataset)
        # Create base output path
        output_base = "s3://hotspot-user-data-dev"

        # Run subgroup discovery
        results = run_subgroup_discovery(
            df=df,
            target_attribute=args.target_attribute,
            target_value=args.target_value,
            max_features=args.max_features,
            job_id=args.job_id,
            dataset_path=args.dataset
        )

        # Save final results to S3
        success = save_results_to_s3(results, args.output_path,args.job_id )

        if success:
            logger.info("Job completed successfully")
            sys.exit(0)
        else:
            logger.error("Failed to save results")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Error in subgroup discovery job: {e}", exc_info=True)
        sys.exit(1)
