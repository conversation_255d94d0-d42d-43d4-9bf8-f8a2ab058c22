import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import { harmonyAPIGatewayAccessRole, hotspotApiEndpoint, hotspotApiRegion } from './config';

window.harmony.authorization.registerApi({
  url: hotspotApiEndpoint,
  roleArn: harmonyAPIGatewayAccessRole,
  region: hotspotApiRegion,
});

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);