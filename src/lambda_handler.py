import json
import os
import logging
import sys
import traceback
import boto3
import uuid
from botocore.exceptions import ClientError


logger = logging.getLogger()
logger.setLevel(logging.INFO)


try:
    s3_client = boto3.client('s3')
    user_data_bucket = os.environ.get('USER_DATA_BUCKET')
    logger.info(
        f"Initialized S3 client with user data bucket: {user_data_bucket}")
except Exception as e:
    logger.error(f"Error initializing S3 client: {str(e)}")
    logger.error(f"Stack trace: {traceback.format_exc()}")
    s3_client = None
    user_data_bucket = None


def get_presigned_url(s3, bucket_name, file_key, content_type, expires_in=3600):
    """
    Generate a presigned URL for uploading a file to S3
    This function is extracted to make testing easier
    """
    try:
        presigned_url = s3.generate_presigned_url(
            'put_object',
            Params={
                'Bucket': bucket_name,
                'Key': file_key,
                'ContentType': content_type
            },
            ExpiresIn=expires_in
        )
        logger.info(f"Generated presigned URL for {file_key}")
        return presigned_url, None
    except ClientError as e:
        logger.error(f"Error generating presigned URL: {e}")
        return None, str(e)
    except Exception as e:
        logger.error(f"Unexpected error generating presigned URL: {e}")
        logger.error(f"Stack trace: {traceback.format_exc()}")
        return None, str(e)


def get_presigned_url_handler(event, context, s3=None, bucket_name=None):
    """Lambda handler to generate a presigned URL for file upload to S3"""

    s3 = s3 or s3_client
    bucket_name = bucket_name or user_data_bucket

    try:
        logger.info("Starting get_presigned_url_handler")

        body = json.loads(event.get('body', '{}'))
        logger.info(f"Request body: {body}")

        file_name = body.get('fileName')
        file_type = body.get('fileType')

        if not file_name or not file_type:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                    'Access-Control-Allow-Methods': 'POST'
                },
                'body': json.dumps({
                    'message': 'fileName and fileType are required'
                })
            }

        file_extension = os.path.splitext(file_name)[1].lower()
        if file_extension != '.csv':
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                    'Access-Control-Allow-Methods': 'POST'
                },
                'body': json.dumps({
                    'message': 'Only CSV files are supported'
                })
            }

        if file_type != 'text/csv' and not file_type.startswith('application/csv'):
            logger.warning(
                f"Received non-CSV content type: {file_type}, but continuing as file extension is CSV")

        unique_key = f"uploads/{uuid.uuid4()}.csv"

        presigned_url, error = get_presigned_url(
            s3, bucket_name, unique_key, 'text/csv')

        if error:
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                    'Access-Control-Allow-Methods': 'POST'
                },
                'body': json.dumps({
                    'message': f"Error generating presigned URL: {error}"
                })
            }

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                'Access-Control-Allow-Methods': 'POST'
            },
            'body': json.dumps({
                'presignedUrl': presigned_url,
                'key': unique_key,
                'fileName': file_name,
                'fileType': 'text/csv',
                'bucket': bucket_name
            })
        }
    except Exception as e:
        logger.error(f"Error generating presigned URL: {str(e)}")
        logger.error(f"Stack trace: {traceback.format_exc()}")
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                'Access-Control-Allow-Methods': 'POST'
            },
            'body': json.dumps({
                'message': f"Error generating presigned URL: {str(e)}",
                'error_type': type(e).__name__,
                'stack_trace': traceback.format_exc()
            })
        }
