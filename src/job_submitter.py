import json
import os
import boto3
import logging
import requests
from typing import Dict, Any, Optional
import uuid
from datetime import datetime, timedelta
from botocore.exceptions import ClientError
import decimal
from decimal import Decimal  


logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
for handler in logger.handlers:
    handler.setFormatter(formatter)


RAY_CLUSTER_ADDRESS = os.environ.get("RAY_CLUSTER_ADDRESS")
EMR_CLUSTER_ID = os.environ.get("EMR_CLUSTER_ID")
AWS_REGION = os.environ.get("AWS_REGION", "us-east-1")
USER_DATA_BUCKET = os.environ.get('USER_DATA_BUCKET', '')
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'dev')
JOBS_TABLE = os.environ.get(
    'RAY_JOBS_TABLE', f'hotspot-detection-{ENVIRONMENT}-analysis-jobs')

logger.info(
    f"Initialized Lambda with: EMR_CLUSTER_ID={EMR_CLUSTER_ID}, RAY_CLUSTER_ADDRESS={RAY_CLUSTER_ADDRESS}, ENVIRONMENT={ENVIRONMENT}")


emr_client = boto3.client('emr', region_name=AWS_REGION)
dynamodb = boto3.resource('dynamodb')
s3 = boto3.client('s3')


jobs_table = dynamodb.Table(JOBS_TABLE)


class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return float(o) if o % 1 != 0 else int(o)
        return super(DecimalEncoder, self).default(o)


def verify_cluster_running() -> bool:
    """Check if the EMR cluster is running and available"""
    logger.info(f"Verifying EMR cluster {EMR_CLUSTER_ID} is running")
    try:
        response = emr_client.describe_cluster(ClusterId=EMR_CLUSTER_ID)
        state = response.get('Cluster', {}).get('Status', {}).get('State')
        if state == 'RUNNING':
            logger.info(f"EMR Cluster {EMR_CLUSTER_ID} is running")
            return True
        else:
            logger.warning(
                f"EMR Cluster {EMR_CLUSTER_ID} is in state {state}, not RUNNING")
            return False
    except Exception as e:
        logger.error(
            f"Error checking EMR cluster status: {str(e)}", exc_info=True)
        return False


def ray_status_check() -> bool:
    """Check if the processing engine is running on the cluster"""
    logger.info(f"Checking Ray cluster status at {RAY_CLUSTER_ADDRESS}:8265")
    try:

        response = requests.get(
            f"http://{RAY_CLUSTER_ADDRESS}:8265/api/cluster_status")
        if response.status_code == 200:
            logger.info("Ray cluster is running successfully")
            return True
        else:
            logger.warning(
                f"Ray cluster check failed with status code {response.status_code}, response: {response.text[:200]}")
            return False
    except Exception as e:
        logger.error(
            f"Error checking Ray cluster status: {str(e)}", exc_info=True)
        return False


def check_job_status(job_id: str) -> Dict[str, Any]:
    """Check the status of a submitted job"""
    logger.info(f"Checking EMR step status for job_id: {job_id}")
    try:
        response = emr_client.describe_step(
            ClusterId=EMR_CLUSTER_ID,
            StepId=job_id
        )

        step = response.get("Step", {})
        status = step.get("Status", {})
        state = status.get("State", "UNKNOWN")

        logger.info(f"Job {job_id} status: {state}")
        return {
            "statusCode": 200,
            "body": json.dumps({
                "job_id": job_id,
                "status": state,
                "details": status.get("StateChangeReason", {})
            })
        }

    except Exception as e:
        logger.error(
            f"Error checking job status for {job_id}: {str(e)}", exc_info=True)
        return {
            "statusCode": 500,
            "body": json.dumps({"error": str(e)})
        }


def handler(event, context):
    """
    Main handler function for the Analysis Job Submitter Lambda.
    Handles job submission, status checks, and history retrieval.
    """
    request_id = context.aws_request_id
    logger.info(
        f"[RequestID: {request_id}] Processing request, remaining time: {context.get_remaining_time_in_millis()}ms")

    request_context = event.get('requestContext', {})
    http_method = request_context.get('httpMethod', '')
    resource_path = request_context.get('resourcePath', '')

    logger.info(
        f"[RequestID: {request_id}] HTTP Method: {http_method}, Resource Path: {resource_path}")

    request_identity = request_context.get('identity', {})
    caller_user_id = request_identity.get('userArn', 'anonymous')
    if '/' in caller_user_id:
        caller_user_id = caller_user_id.split('/')[-1]

    logger.info(f"[RequestID: {request_id}] User: {caller_user_id}")

    if resource_path.endswith('/analysis-jobs/submit') and http_method == 'POST':
        logger.info(
            f"[RequestID: {request_id}] Handling job submission request")
        return submit_job(event, caller_user_id)
    elif resource_path.endswith('/analysis-jobs/status') and http_method == 'GET':
        logger.info(f"[RequestID: {request_id}] Handling job status request")
        return get_job_status(event)
    elif resource_path.endswith('/analysis-jobs/history') and http_method == 'GET':
        logger.info(f"[RequestID: {request_id}] Handling job history request")
        return get_job_history(event, caller_user_id)
    elif resource_path.endswith('/analysis-jobs/results') and http_method == 'GET':
        logger.info(f"[RequestID: {request_id}] Handling job graph request")
        return get_job_graph(event)
    else:
        logger.warning(
            f"[RequestID: {request_id}] Invalid request path or method: {resource_path}, {http_method}")
        return create_response(400, {'error': 'Invalid request path or method'})


def submit_job(event, user_id):
    """
    Submit a new analysis job to the cluster.

    Parameters:
    event (dict): The Lambda event
    user_id (str): The ID of the user submitting the job

    Returns:
    dict: API Gateway response with job details
    """
    try:

        body = json.loads(event.get('body', '{}'))
        logger.info(
            f"Submitting job for user: {user_id}, body size: {len(event.get('body', '{}'))} bytes")

        dataset_key = body.get('datasetKey')
        dimensions = body.get('dimensions')
        target_attribute = body.get('targetAttribute')
        target_value = body.get('targetValue')
        max_features = body.get('maxFeatures', 10)
        min_subgroup_size = body.get('minSubgroupSize', 30)
        use_parallel = body.get('useParallel', True)
        job_name = body.get(
            'jobName', f"HotSpot-Analysis-{datetime.now().strftime('%Y%m%d-%H%M%S')}")

        logger.info(f"Job parameters: dataset={dataset_key}, target={target_attribute}={target_value}, " +
                    f"maxFeatures={max_features}, minSubgroupSize={min_subgroup_size}, useParallel={use_parallel}")

        if not dataset_key or not target_attribute or target_value is None:
            logger.warning(f"Missing required parameters for job submission: " +
                           f"datasetKey={'✓' if dataset_key else '✗'}, " +
                           f"targetAttribute={'✓' if target_attribute else '✗'}, " +
                           f"targetValue={'✓' if target_value is not None else '✗'}")
            return create_response(400, {
                'error': 'Missing required parameters',
                'required': ['datasetKey', 'targetAttribute', 'targetValue']
            })

        job_id = str(uuid.uuid4())
        timestamp = datetime.utcnow().isoformat()
        logger.info(f"Created new job with ID: {job_id}")

        script_path = f"s3://{USER_DATA_BUCKET}/scripts/run_subgroup_discovery.py"
        output_path = f"s3://{USER_DATA_BUCKET}/results/{job_id}/"

        logger.info(f"Using script path: {script_path}")
        logger.info(f"Results will be stored at: {output_path}")

        step_config = {
            'Name': job_name,
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 'command-runner.jar',
                'Args': [
                    'bash', '-c',
                    f"aws s3 cp {script_path} /tmp/ray_job.py && "
                    f"sudo yum install -y graphviz && "
                    f"sudo python3.11 -m pip install graphviz && "
                    f"python3.11 -c 'import graphviz' && "
                    f"python3.11 /tmp/ray_job.py "
                    f"--dataset s3://{USER_DATA_BUCKET}/{dataset_key} "
                    f"--target-attribute {target_attribute} "
                    f"--target-value {str(target_value)} "
                    f"--max-features {max_features} "
                    f"--output-path {output_path} "
                    f"--job-id {job_id}"
                ]
            }
        }

        logger.info(f"Prepared EMR step config for job: {job_name}")

        logger.info(f"Adding EMR step to cluster {EMR_CLUSTER_ID}")
        response = emr_client.add_job_flow_steps(
            JobFlowId=EMR_CLUSTER_ID,
            Steps=[step_config]
        )

        emr_step_id = response['StepIds'][0] if response.get(
            'StepIds') else None
        logger.info(f"EMR step created with ID: {emr_step_id}")

        job_item = {
            'jobId': job_id,
            'emrStepId': emr_step_id,
            'emrClusterId': EMR_CLUSTER_ID,
            'userId': user_id,
            'status': 'PENDING',
            'createdAt': timestamp,
            'updatedAt': timestamp,
            'name': job_name,
            'parameters': {
                'datasetKey': dataset_key,
                'dimensions': dimensions,
                'targetAttribute': target_attribute,
                'targetValue': target_value,
                'maxFeatures': max_features,
                'minSubgroupSize': min_subgroup_size,
                'useParallel': use_parallel,
                'outputPath': output_path
            },

            'ttl': int((datetime.utcnow() + timedelta(days=90)).timestamp())
        }

        logger.info(f"Storing job details in DynamoDB table: {JOBS_TABLE}")
        jobs_table.put_item(Item=job_item)
        logger.info(f"Successfully stored job {job_item} in DynamoDB")

        return create_response(201, {
            'jobId': job_id,
            'emrStepId': emr_step_id,
            'name': job_name,
            'status': 'PENDING',
            'createdAt': timestamp,
            'parameters': {
                'datasetKey': dataset_key,
                'dimensions': dimensions,
                'targetAttribute': target_attribute,
                'targetValue': target_value,
                'maxFeatures': max_features,
                'minSubgroupSize': min_subgroup_size,
                'useParallel': use_parallel,
                'outputPath': output_path
            }
        })
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', 'Unknown')
        logger.error(
            f"AWS error in submit_job: Code={error_code}, Message={str(e)}", exc_info=True)
        return create_response(500, {'error': f"AWS error ({error_code}): {str(e)}"})
    except Exception as e:
        logger.error(f"Error in submit_job: {str(e)}", exc_info=True)
        return create_response(500, {'error': f"Error submitting job: {str(e)}"})


def get_job_status(event):
    """
    Get the status of an analysis job.

    Parameters:
    event (dict): The Lambda event

    Returns:
    dict: API Gateway response with job status
    """
    try:

        query_params = event.get('queryStringParameters', {}) or {}
        job_id = query_params.get('jobId')

        logger.info(f"Getting status for job: {job_id}")

        if not job_id:
            logger.warning("Missing jobId parameter in status request")
            return create_response(400, {'error': 'Missing required parameter: jobId'})

        logger.info(f"Fetching job details from DynamoDB for job: {job_id}")
        response = jobs_table.get_item(Key={'jobId': job_id})
        job_item = response.get('Item')

        if not job_item:
            logger.warning(f"Job with ID {job_id} not found in DynamoDB")
            return create_response(404, {'error': f"Job with ID {job_id} not found"})

        emr_step_id = job_item.get('emrStepId')
        emr_cluster_id = job_item.get('emrClusterId', EMR_CLUSTER_ID)
        current_status = job_item.get('status', 'UNKNOWN')

        logger.info(
            f"Current status of job {job_id}: {current_status}, EMR step ID: {emr_step_id}")

        if current_status in ['COMPLETED', 'FAILED', 'CANCELLED']:
            logger.info(
                f"Job {job_id} is in terminal state {current_status}, returning stored status")
            return create_response(200, {
                'jobId': job_id,
                'status': current_status,
                'emrStepId': emr_step_id,
                'lastUpdated': job_item.get('updatedAt'),
                'name': job_item.get('name'),
                'parameters': job_item.get('parameters', {}),
                'results': job_item.get('results', {})
            })

        step_status = 'PENDING'
        if emr_step_id and emr_cluster_id:
            try:
                logger.info(
                    f"Checking EMR step status for step {emr_step_id} on cluster {emr_cluster_id}")
                step_response = emr_client.describe_step(
                    ClusterId=emr_cluster_id,
                    StepId=emr_step_id
                )
                step_info = step_response.get('Step', {})
                step_status = step_info.get(
                    'Status', {}).get('State', 'PENDING')
                logger.info(f"EMR step {emr_step_id} status: {step_status}")
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', 'Unknown')
                logger.warning(
                    f"Error getting EMR step status: Code={error_code}, Message={str(e)}")

                step_status = current_status
                logger.info(f"Falling back to stored status: {step_status}")

        status_mapping = {
            'PENDING': 'PENDING',
            'RUNNING': 'RUNNING',
            'COMPLETED': 'COMPLETED',
            'CANCELLED': 'CANCELLED',
            'FAILED': 'FAILED',
            'INTERRUPTED': 'FAILED'
        }

        job_status = status_mapping.get(step_status, step_status)
        logger.info(
            f"Mapped EMR status {step_status} to job status {job_status}")

        if job_status != current_status:
            logger.info(
                f"Updating job status in DynamoDB from {current_status} to {job_status}")
            timestamp = datetime.utcnow().isoformat()
            update_expr = "set #status = :status, #updatedAt = :updatedAt"
            expr_attr_names = {'#status': 'status', '#updatedAt': 'updatedAt'}
            expr_attr_values = {':status': job_status, ':updatedAt': timestamp}

            if job_status == 'COMPLETED':
                output_path = job_item.get(
                    'parameters', {}).get('outputPath', '')
                logger.info(
                    f"Job completed, checking for results at {output_path}")
                results = get_job_results(output_path)
                if results:
                    logger.info(
                        f"Found results for job {job_id}, updating DynamoDB")
                    update_expr += ", #results = :results"
                    expr_attr_names['#results'] = 'results'
                    # Convert float values in results to Decimal
                    results = json.loads(json.dumps(results), parse_float=Decimal)
                    expr_attr_values[':results'] = results
                else:
                    logger.warning(
                        f"No results found for completed job {job_id} at {output_path}")

            logger.info(f"Updating job {job_id} in DynamoDB with new status")
            # Convert float values in expr_attr_values to Decimal
            expr_attr_values = {
                k: (Decimal(str(v)) if isinstance(v, float) else v)
                for k, v in expr_attr_values.items()
            }
            jobs_table.update_item(
                Key={'jobId': job_id},
                UpdateExpression=update_expr,
                ExpressionAttributeNames=expr_attr_names,
                ExpressionAttributeValues=expr_attr_values
            )
            logger.info(
                f"Successfully updated job {job_id} status to {job_status}")

            logger.info(f"Fetching updated job details for {job_id}")
            response = jobs_table.get_item(Key={'jobId': job_id})
            job_item = response.get('Item', {})
            logger.info(f"Retrieved updated job details")

        return create_response(200, {
            'jobId': job_id,
            'status': job_status,
            'emrStepId': emr_step_id,
            'lastUpdated': job_item.get('updatedAt'),
            'name': job_item.get('name'),
            'parameters': job_item.get('parameters', {}),
            'results': job_item.get('results', {})
        })
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', 'Unknown')
        logger.error(
            f"AWS error in get_job_status: Code={error_code}, Message={str(e)}", exc_info=True)
        return create_response(500, {'error': f"AWS error ({error_code}): {str(e)}"})
    except Exception as e:
        logger.error(f"Error in get_job_status: {str(e)}", exc_info=True)
        return create_response(500, {'error': f"Error getting job status: {str(e)}"})


def get_job_history(event, user_id):
    """
    Get the history of analysis jobs for the user.

    Parameters:
    event (dict): The Lambda event
    user_id (str): The ID of the user

    Returns:
    dict: API Gateway response with job history
    """
    try:
        logger.info(f"Getting job history for user: {user_id}")

        query_params = event.get('queryStringParameters', {}) or {}
        limit = int(query_params.get('limit', 10)) # To limit the no of job items in the history
        start_date = query_params.get('startDate')
        end_date = query_params.get('endDate')
        status_filter = query_params.get('status')
        next_token = query_params.get('nextToken')

        logger.info(
            f"Query parameters: limit={limit}, startDate={start_date}, endDate={end_date}, status={status_filter}, hasNextToken={next_token is not None}")

        if limit < 1 or limit > 100:
            logger.warning(
                f"Invalid limit parameter: {limit}, using default 20")
            limit = 20

        index_name = 'UserTimeIndex'
        key_condition_expression = boto3.dynamodb.conditions.Key(
            'userId').eq(user_id)

        logger.info(f"Using DynamoDB index: {index_name} for user: {user_id}")

        if start_date and end_date:
            logger.info(
                f"Adding date range filter: {start_date} to {end_date}")
            key_condition_expression = key_condition_expression & \
                boto3.dynamodb.conditions.Key(
                    'createdAt').between(start_date, end_date)
        elif start_date:
            logger.info(f"Adding start date filter: from {start_date}")
            key_condition_expression = key_condition_expression & \
                boto3.dynamodb.conditions.Key('createdAt').gte(start_date)
        elif end_date:
            logger.info(f"Adding end date filter: until {end_date}")
            key_condition_expression = key_condition_expression & \
                boto3.dynamodb.conditions.Key('createdAt').lte(end_date)

        query_params = {
            'IndexName': index_name,
            'KeyConditionExpression': key_condition_expression,
            'Limit': limit,
            'ScanIndexForward': False
        }

        if status_filter:
            logger.info(f"Adding status filter: {status_filter}")
            query_params['FilterExpression'] = boto3.dynamodb.conditions.Attr(
                'status').eq(status_filter)

        if next_token:
            logger.info(f"Using pagination token for continuation")
            query_params['ExclusiveStartKey'] = json.loads(next_token)

        logger.info(f"Executing DynamoDB query on table: {JOBS_TABLE}")
        response = jobs_table.query(**query_params)

        items = response.get('Items', [])
        logger.info(
            f"Query returned {len(items)} items out of {response.get('ScannedCount', 0)} scanned")

        for item in items:
            for key, value in item.items():
                if isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, decimal.Decimal):
                            logger.debug(
                                f"Found Decimal value in item[{key}][{sub_key}]: {sub_value}")

        result = {
            'jobs': items,
            'count': len(items),
            'scannedCount': response.get('ScannedCount', 0)
        }

        if 'LastEvaluatedKey' in response:
            last_key = response['LastEvaluatedKey']
            logger.info(
                f"More results available, returning pagination token for continuation")
            result['nextToken'] = json.dumps(last_key)

        logger.info(f"Successfully retrieved job history for user {user_id}")
        return create_response(200, result)
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', 'Unknown')
        logger.error(
            f"AWS error in get_job_history: Code={error_code}, Message={str(e)}", exc_info=True)
        return create_response(500, {'error': f"AWS error ({error_code}): {str(e)}"})
    except Exception as e:
        logger.error(f"Error in get_job_history: {str(e)}", exc_info=True)
        return create_response(500, {'error': f"Error retrieving job history: {str(e)}"})


def get_job_results(output_path):
    """
    Get the results of a completed job from S3.

    Parameters:
    output_path (str): S3 path where results are stored

    Returns:
    dict: Job results
    """
    try:
        logger.info(f"Getting job results from: {output_path}")

        if not output_path or not output_path.startswith('s3://'):
            logger.warning(f"Invalid output path: {output_path}")
            return None

        path_parts = output_path.replace('s3://', '').split('/')
        bucket = path_parts[0]
        prefix = '/'.join(path_parts[1:])

        logger.info(f"Parsed S3 path - bucket: {bucket}, prefix: {prefix}")

        results_key = f"{prefix.rstrip('/')}/results.json"
        logger.info(f"Looking for results file at: {bucket}/{results_key}")

        try:
            logger.info(
                f"Attempting to get object: s3://{bucket}/{results_key}")
            response = s3.get_object(Bucket=bucket, Key=results_key)
            content = response['Body'].read().decode('utf-8')
            logger.info(
                f"Successfully retrieved results, content size: {len(content)} bytes")
            return json.loads(content)
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            if error_code == 'NoSuchKey':
                logger.info(
                    f"Results file not found at {bucket}/{results_key}")
                return None
            logger.error(
                f"S3 error getting results: Code={error_code}, Message={str(e)}")
            raise
    except Exception as e:
        logger.error(
            f"Error getting job results from {output_path}: {str(e)}", exc_info=True)
        return None
def generate_presigned_url(bucket: str, key: str, expires_in: int = 3600) -> tuple[str, str]:
    """
    Generate a pre-signed URL for accessing an S3 object.

    Parameters:
    bucket (str): S3 bucket name
    key (str): S3 object key
    expires_in (int): URL expiration time in seconds (default: 1 hour)

    Returns:
    tuple[str, str]: (presigned_url, error_message)
    """
    try:
        presigned_url = s3.generate_presigned_url(
            'get_object',
            Params={
                'Bucket': bucket,
                'Key': key
            },
            ExpiresIn=expires_in
        )
        logger.info(f"Generated presigned URL for {bucket}/{key}")
        return presigned_url, None
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', 'Unknown')
        error_message = str(e)
        logger.error(f"Error generating presigned URL: Code={error_code}, Message={error_message}")
        return None, error_message
    except Exception as e:
        error_message = str(e)
        logger.error(f"Unexpected error generating presigned URL: {error_message}")
        return None, error_message

def get_job_graph(event):
    """
    Get the graph PDF for a completed job.

    Parameters:
    event (dict): The Lambda event

    Returns:
    dict: API Gateway response with pre-signed URL for the graph
    """
    logger.info("getting the graph pdf")
    try:
        query_params = event.get('queryStringParameters', {}) or {}
        job_id = query_params.get('jobId')

        if not job_id:
            logger.warning("Missing jobId parameter in graph request")
            return create_response(400, {'error': 'Missing required parameter: jobId'})

        # Verify job exists and status
        logger.info(f"Fetching job details from DynamoDB for job: {job_id}")
        response = jobs_table.get_item(Key={'jobId': job_id})
        job_item = response.get('Item')

        if not job_item:
            logger.warning(f"Job with ID {job_id} not found")
            return create_response(404, {'error': 'Job not found'})

        current_status = job_item.get('status', 'UNKNOWN')
        if current_status != 'COMPLETED':
            logger.warning(f"Job {job_id} is not completed (status: {current_status})")
            return create_response(400, {'error': 'Job results not available yet'})

        # Get output path from job parameters
        output_path = job_item.get('parameters', {}).get('outputPath', '')
        if not output_path:
            logger.error(f"No output path found for job {job_id}")
            return create_response(500, {'error': 'Job output path not found'})

        # Construct graph file path
        path_parts = output_path.replace('s3://', '').split('/')
        bucket = path_parts[0]
        prefix = '/'.join(path_parts[1:])
        graph_key = f"{prefix.rstrip('/')}/subgroup_graph.pdf"

        logger.info(f"Looking for graph file at: {bucket}/{graph_key}")

        try:
            # Verify graph file exists
            s3.head_object(Bucket=bucket, Key=graph_key)
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            if error_code == 'NoSuchKey' or error_code == '404':
                logger.warning(f"Graph file not found at {bucket}/{graph_key}")
                return create_response(404, {'error': 'Graph file not found'})
            logger.error(f"Error checking graph file: Code={error_code}, Message={str(e)}")
            return create_response(500, {'error': f'Error accessing graph file: {str(e)}'})

        # Generate pre-signed URL
        graph_url, error = generate_presigned_url(bucket, graph_key)
        if error:
            logger.error(f"Failed to generate pre-signed URL: {error}")
            return create_response(500, {'error': f'Failed to generate graph URL: {error}'})

        logger.info(f"Successfully generated graph URL for job {job_id}")
        return create_response(200, {
            'graphUrl': graph_url,
            'jobId': job_id,
            'fileName': 'subgroup_graph.pdf'
        })

    except Exception as e:
        logger.error(f"Error in get_job_graph: {str(e)}", exc_info=True)
        return create_response(500, {'error': f"Error retrieving graph: {str(e)}"})


def create_response(status_code, body):
    """
    Create an API Gateway response.

    Parameters:
    status_code (int): HTTP status code
    body (dict): Response body

    Returns:
    dict: API Gateway response
    """
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': os.environ.get('CORS_ORIGIN', '*'),
            'Access-Control-Allow-Headers': '*',
            'Access-Control-Allow-Methods': 'OPTIONS,GET,POST'
        },
        'body': json.dumps(body, cls=DecimalEncoder)
    }
