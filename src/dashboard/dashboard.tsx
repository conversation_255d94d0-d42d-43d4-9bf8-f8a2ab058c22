import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { QuickCreatePanel } from "@/components/dataset-upload/quick-create-panel"
import { JobHistoryPanel } from "@/components/jobs/job-history-panel"
import { useState, useEffect } from "react"

export type DashboardView = "quick-create" | "job-history";

export default function Dashboard() {
  const [activeView, setActiveView] = useState<DashboardView>("quick-create");


  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash;
      if (hash === "#job-history") {
        setActiveView("job-history");
      } else if (hash === "#quick-create" || hash === "#dataset-upload") {
        setActiveView("quick-create");
      }
    };


    handleHashChange();


    window.addEventListener("hashchange", handleHashChange);
    return () => window.removeEventListener("hashchange", handleHashChange);
  }, []);

  return (
    <SidebarProvider>
      <AppSidebar variant="inset" setActiveView={setActiveView} />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 p-6">
              {activeView === "quick-create" ? (
                <>
                  <h1 className="text-2xl font-semibold">Create Analysis</h1>
                  <QuickCreatePanel />
                </>
              ) : activeView === "job-history" ? (
                <>
                  <h1 className="text-2xl font-semibold">Analysis Results</h1>
                  <div className="bg-background rounded-lg">
                    <JobHistoryPanel />
                  </div>
                </>
              ) : null}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
